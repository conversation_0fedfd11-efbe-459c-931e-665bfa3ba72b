package com.fuiou.dips.utils;

import org.apache.log4j.Logger;

import java.io.InputStream;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
/**
 * 
 * <AUTHOR>
 *
 */
public class ConfigReader {

	private static Logger logger = Logger.getLogger(ConfigReader.class);

	private static Properties RESOURCE_BUNDLE = null;

	private static final String FUIOU_PATH = "fuiou.properties";
	//private static final String FUIOU_PATH = "fuiou-dev.properties";

	static {
		new Thread(new Runnable() {
			public void run() {
				while (true) {
					try {
						TimeUnit.SECONDS.sleep(30);
						refreshProp();
					} catch (InterruptedException e) {
						LogWriter.error("异常",e);
					}
				}
			}
		}).start();
	}

	public synchronized static void init() throws Exception {
		if (RESOURCE_BUNDLE == null) {
			refreshProp();
		}
	}

	private static void refreshProp() {
		try {
			Properties temp = new Properties();
			InputStream fi = (InputStream) ConfigReader.class.getClassLoader().getResourceAsStream(FUIOU_PATH);
			temp.load(fi);
			RESOURCE_BUNDLE = temp;
		} catch (Exception e) {
			LogWriter.error("异常",e);
		}
	}

	/**
	 * @param propName
	 * @param key
	 */
	public static String getConfig(String key) {

		try {
			if (RESOURCE_BUNDLE == null) {
				init();
			}
			if (RESOURCE_BUNDLE.containsKey(key)) {
				String value = RESOURCE_BUNDLE.getProperty(key);
				return value;
			}
		} catch (Throwable e) {
			logger.error("后台获取配置文件失败，key=" + key + "错误原因为：", e);
			return "";
		}
		return "";
	}

	public static int getInt(String key){
		return Integer.valueOf(getConfig(key));
	}
	
	public static void main(String[] args) {
		String value=ConfigReader.getConfig("ENV_PARAM");
		System.out.println(value);
	}
}
