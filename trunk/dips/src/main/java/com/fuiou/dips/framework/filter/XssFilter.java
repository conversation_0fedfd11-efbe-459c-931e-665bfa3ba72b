package com.fuiou.dips.framework.filter;

import com.fuiou.dips.utils.LogWriter;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;



/**
* 通过Filter过滤器来防XSS
*
*/
public class XssFilter implements Filter{
	private FilterConfig config;
	//private static final String APOSTROPHE = "apostrophe";
	private static boolean no_init = true;
	private boolean rejIgalReq;
	
	private String illegalPage;
	
	private String encoding="UTF-8";

	public XssFilter() {
		config = null;
		rejIgalReq = false;
	}

	public void init(FilterConfig filterconfig) throws ServletException {
		config = filterconfig;
		no_init = false;
		initRejIgalReq(filterconfig);
	}
	
	private void initRejIgalReq(FilterConfig filterconfig) {
		try{
		String s = filterconfig.getInitParameter("rejIgalReq");
		this.illegalPage=filterconfig.getInitParameter("illegalPage");
		if(filterconfig.getInitParameter("encoding")!=null)
			this.encoding=filterconfig.getInitParameter("encoding");
		if (s != null) 
			rejIgalReq = Boolean.parseBoolean(s.trim());
		}catch(Exception e){
			LogWriter.error("异常",e);
			//do nothing
			rejIgalReq=false;
		}
		
	}

	public void destroy() {
		config = null;
	}

	public FilterConfig getFilterConfig() {
		return config;
	}

	public void setFilterConfig(FilterConfig filterconfig) {
		if (no_init) {
			no_init = false;
			config = filterconfig;
			initRejIgalReq(filterconfig);
		}
	}
	
	

	public void doFilter(ServletRequest servletrequest, ServletResponse servletresponse, FilterChain filterchain)
			throws IOException, ServletException {
		if(rejIgalReq&&hasIgalParam((HttpServletRequest) servletrequest))
			((HttpServletResponse)servletresponse).sendRedirect(((HttpServletRequest) servletrequest).getContextPath()+illegalPage);
		else
			//filterchain.doFilter((HttpServletRequest) servletrequest, servletresponse);
	filterchain.doFilter(new XssRequestWrapper((HttpServletRequest) servletrequest), servletresponse);
	}

	private boolean hasIgalParam(HttpServletRequest request) {
		initEncoding(request);
		for(String[]strs:request.getParameterMap().values()){
			if(strs==null||strs.length==0)
				continue;
			for (String str : strs) {
				if(str==null)
					continue;
				/*if(str.contains("<p>") && str.contains("</p>"))
					continue;
				if(!str.trim().equals(XssCleaner.xssClean(str.trim()))){
					System.out.println("============================"+str);
					return true;
				}*/
				if(str.contains("<script>") && str.contains("</script>"))
					return true;
			}
		}
		return false;
	}

	private void initEncoding(HttpServletRequest request) {
		if (this.encoding != null && (request.getCharacterEncoding() == null))
			try {
				request.setCharacterEncoding(this.encoding);
			} catch (UnsupportedEncodingException e) {
				LogWriter.error("异常",e);
				//do nothing
			}

	}
}
