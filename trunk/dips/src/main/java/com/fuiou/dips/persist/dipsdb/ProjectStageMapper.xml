<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.dips.persist.dipsdb.ProjectStageMapper">

    <resultMap id="BaseResultMap" type="com.fuiou.dips.persist.beans.ProjectStage">
        <id property="rowId" column="row_id" jdbcType="BIGINT"/>
        <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
        <result property="mchntCd" column="mchnt_cd" jdbcType="CHAR"/>
        <result property="stageNo" column="stage_no" jdbcType="VARCHAR"/>
        <result property="stageOrder" column="stage_order" jdbcType="INTEGER"/>
        <result property="stageName" column="stage_name" jdbcType="VARCHAR"/>
        <result property="stageAmt" column="stage_amt" jdbcType="DECIMAL"/>
        <result property="stageActualAmt" column="stage_actual_amt" jdbcType="DECIMAL"/>
        <result property="refundAmt" column="refund_amt" jdbcType="DECIMAL"/>
        <result property="stageSt" column="stage_st" jdbcType="CHAR"/>
        <result property="lockFlag" column="lock_flag" jdbcType="CHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="reserved1" column="reserved1" jdbcType="VARCHAR"/>
        <result property="reserved2" column="reserved2" jdbcType="VARCHAR"/>
        <result property="reserved3" column="reserved3" jdbcType="VARCHAR"/>
        <result property="deleteState" column="is_delete" javaType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        row_id
        ,project_no,mchnt_cd,
        stage_no,stage_order,stage_name,
        stage_amt,stage_actual_amt,refund_amt,
        stage_st,lock_flag,create_time,
        update_time,reserved1,reserved2,
        reserved3,is_delete
    </sql>

    <insert id="insert" keyColumn="row_id" keyProperty="rowId"
            parameterType="com.fuiou.dips.persist.beans.ProjectStage" useGeneratedKeys="true">
        insert into t_dips_project_stage
        (project_no, mchnt_cd, stage_no, stage_order, stage_name, stage_amt, stage_st)
        values (#{projectNo,jdbcType=VARCHAR}, #{mchntCd,jdbcType=CHAR}, #{stageNo,jdbcType=VARCHAR},
                #{stageOrder,jdbcType=INTEGER}, #{stageName,jdbcType=VARCHAR}, #{stageAmt,jdbcType=DECIMAL},
                #{stageSt,jdbcType=CHAR})
    </insert>

    <select id="selectByProjectNoAndMchntCd" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_dips_project_stage
        where project_no = #{projectNo,jdbcType=VARCHAR}
        and mchnt_cd = #{mchntCd,jdbcType=CHAR}
        and is_delete = 0
        order by stage_order asc
    </select>

    <select id="selectByProjectNos" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_dips_project_stage
        <where>
            is_delete = 0
            <if test="mchntCd != null and mchntCd != ''">
                AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
            </if>
            <if test="projectNos != null and !projectNos.isEmpty()">
                AND project_no IN
                <foreach collection="projectNos" item="projectNo" open="(" separator="," close=")">
                    #{projectNo}
                </foreach>
            </if>

            <if test="stageSts != null and !stageSts.isEmpty()">
                AND stage_st IN
                <foreach collection="stageSts" item="stageSt" open="(" separator="," close=")">
                    #{stageSt}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectFirstStageByProjectNoAndMchntCd" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_dips_project_stage
        where project_no = #{projectNo,jdbcType=VARCHAR}
        and mchnt_cd = #{mchntCd,jdbcType=CHAR}
        and stage_order = 1
        and is_delete = 0
        limit 1
    </select>

    <select id="selectStageByProjectNoAndMchntCdAndNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_dips_project_stage
        where project_no = #{projectNo,jdbcType=VARCHAR}
        and mchnt_cd = #{mchntCd,jdbcType=CHAR}
        and stage_no = #{stageNo,jdbcType=VARCHAR}
        and is_delete = 0
    </select>

    <update id="updateByPrimaryKey">
        update t_dips_project_stage
        <set>
            <if test="stageName != null">
                stage_name = #{stageName,jdbcType=VARCHAR},
            </if>
            <if test="stageAmt != null">
                stage_amt = #{stageAmt,jdbcType=DECIMAL},
            </if>
            <if test="reserved1 != null">
                reserved1 = #{reserved1,jdbcType=VARCHAR},
            </if>
            update_time = now(),
        </set>
        where row_id = #{rowId,jdbcType=BIGINT}
        and is_delete = 0
        and lock_flag = '0'
    </update>

    <update id="updateByStageNo">
        update t_dips_project_stage
        <set>
            <if test="stageName != null">
                stage_name = #{stageName,jdbcType=VARCHAR},
            </if>
            <if test="stageAmt != null">
                stage_amt = #{stageAmt,jdbcType=DECIMAL},
            </if>
            <if test="stageOrder != null">
                stage_order = #{stageOrder,jdbcType=INTEGER},
            </if>
            update_time = now(),
        </set>
        where project_no = #{projectNo,jdbcType=VARCHAR}
        and mchnt_cd = #{mchntCd,jdbcType=CHAR}
        and stage_no = #{stageNo,jdbcType=BIGINT}
        and is_delete = 0
        and (stage_st = '0' or (stage_st = '1' and (stage_actual_amt is null or stage_actual_amt = 0)))
        and lock_flag = '0'
    </update>

    <update id="deleteByProjectNoAndMchntCd">
        update t_dips_project_stage
        set is_delete   = 1,
            update_time = now()
        where project_no = #{projectNo,jdbcType=VARCHAR}
          and mchnt_cd = #{mchntCd,jdbcType=CHAR}
    </update>

    <update id="deleteByStageNo">
        update t_dips_project_stage
        set is_delete = 1,
        update_time = now()
        where project_no = #{projectNo,jdbcType=VARCHAR}
        and mchnt_cd = #{mchntCd,jdbcType=CHAR}
        and (stage_st = '0' or (stage_st = '1' and (stage_actual_amt is null or stage_actual_amt = 0)))
        and lock_flag = '0'
        and is_delete = 0
        and stage_no in
        <foreach collection="stageNos" item="stageNo" open="(" separator="," close=")">
            #{stageNo}
        </foreach>
    </update>

    <select id="selectByMchntCdAndProjectNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_dips_project_stage
        where is_delete = 0
        AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
        AND project_no = #{projectNo,jdbcType=VARCHAR}
        ORDER BY stage_order ASC
    </select>


    <update id="updateLock" >
        UPDATE t_dips_project_stage
        SET
            lock_flag = '1',
            update_time = now()

        WHERE project_no = #{projectNo,jdbcType=VARCHAR}
          AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
          AND stage_no = #{stageNo,jdbcType=VARCHAR}
          AND lock_flag = '0'
    </update>

    <update id="updateUnLock" >
        UPDATE t_dips_project_stage
        SET
            lock_flag = '0',
            update_time = now()

        WHERE project_no = #{projectNo,jdbcType=VARCHAR}
          AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
          AND stage_no = #{stageNo,jdbcType=VARCHAR}
          AND lock_flag = '1'
    </update>

    <update id="batchUpdateLock">
        UPDATE t_dips_project_stage
        SET
            lock_flag = '1',
            update_time = now()
        WHERE project_no = #{projectNo,jdbcType=VARCHAR}
          AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
          AND lock_flag = '0'
          AND is_delete = 0
          <if test="stageNos != null and !stageNos.isEmpty()">
              AND stage_no IN
              <foreach collection="stageNos" item="stageNo" open="(" separator="," close=")">
                  #{stageNo}
              </foreach>
          </if>
    </update>

    <update id="batchUpdateUnLock">
        UPDATE t_dips_project_stage
        SET
            lock_flag = '0',
            update_time = now()
        WHERE project_no = #{projectNo,jdbcType=VARCHAR}
          AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
          AND lock_flag = '1'
          AND is_delete = 0
          <if test="stageNos != null and !stageNos.isEmpty()">
              AND stage_no IN
              <foreach collection="stageNos" item="stageNo" open="(" separator="," close=")">
                  #{stageNo}
              </foreach>
          </if>
    </update>

    <update id="updateStageForTxn" parameterType="com.fuiou.dips.persist.beans.ProjectStage">
        UPDATE t_dips_project_stage
        SET
            stage_actual_amt = #{stageActualAmt,jdbcType=DECIMAL},
            lock_flag = '0',
            stage_st = #{stageSt,jdbcType=CHAR},
            update_time = now()

        WHERE project_no = #{projectNo,jdbcType=VARCHAR}
          AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
        AND stage_no = #{stageNo,jdbcType=VARCHAR}
        AND stage_actual_amt = #{oldStageActualAmt,jdbcType=DECIMAL}
        AND lock_flag = '1'
    </update>

    <update id="updateStageForTxnWithoutUnLock" parameterType="com.fuiou.dips.persist.beans.ProjectStage">
        UPDATE t_dips_project_stage
        SET
            stage_actual_amt = #{stageActualAmt,jdbcType=DECIMAL},
            stage_st = #{stageSt,jdbcType=CHAR},
            update_time = now()

        WHERE project_no = #{projectNo,jdbcType=VARCHAR}
          AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
          AND stage_no = #{stageNo,jdbcType=VARCHAR}
          AND stage_actual_amt = #{oldStageActualAmt,jdbcType=DECIMAL}
          AND lock_flag = '1'
    </update>

    <update id="updateStageForRefund" parameterType="com.fuiou.dips.persist.beans.ProjectStage">
        UPDATE t_dips_project_stage
        SET
            refund_amt = #{refundAmt,jdbcType=DECIMAL},
            lock_flag = '0',
            update_time = now()

        WHERE project_no = #{projectNo,jdbcType=VARCHAR}
          AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
          AND stage_no = #{stageNo,jdbcType=VARCHAR}
          AND refund_amt = #{oldRefundAmt,jdbcType=DECIMAL}
          AND lock_flag = '1'
    </update>

    <update id="startStageForTxn" >
        UPDATE t_dips_project_stage
        SET
            stage_st = '1',
            update_time = now()

        WHERE project_no = #{projectNo,jdbcType=VARCHAR}
          AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
          AND stage_no = #{stageNo,jdbcType=VARCHAR}
          AND stage_st = '0'
    </update>
</mapper>
