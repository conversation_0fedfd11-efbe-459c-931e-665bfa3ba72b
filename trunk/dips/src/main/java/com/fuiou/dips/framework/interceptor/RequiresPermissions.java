package com.fuiou.dips.framework.interceptor;

import com.fuiou.dips.enums.EmployeeRoleTypeEnum;
import com.fuiou.dips.enums.UserTypeEnum;

import java.lang.annotation.*;
import java.util.Arrays;

@Target({ElementType.METHOD, ElementType.TYPE})  // 支持方法和类级别
@Retention(RetentionPolicy.RUNTIME)             // 运行时生效
@Documented                                   // 生成文档
public @interface RequiresPermissions {


    UserTypeEnum[] userType() default {};   //允许用户类型

    boolean allMchntUserType() default false;  //所有商户端用户类型

    EmployeeRoleTypeEnum[] employeeRoleType() default {}; //允许员工角色类型

    String message() default "权限校验失败";




}