package com.fuiou.dips.persist.dipsdb;

import com.fuiou.dips.persist.beans.ProjectStage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 项目阶段
 *
 * <AUTHOR>
 * @description 针对表【t_dips_project_stage(装修通项目阶段付款表)】的数据库操作Mapper
 */
public interface ProjectStageMapper {

    int insert(ProjectStage record);

    List<ProjectStage> selectByProjectNoAndMchntCd(@Param("projectNo") String projectNo,
            @Param("mchntCd") String mchntCd);

    List<ProjectStage> selectByProjectNos(@Param("mchntCd") String mchntCd,
            @Param("projectNos") List<String> projectNos, @Param("stageSts") List<String> stageSts);

    ProjectStage selectFirstStageByProjectNoAndMchntCd(@Param("projectNo") String projectNo,
            @Param("mchntCd") String mchntCd);

    ProjectStage selectStageByProjectNoAndMchntCdAndNo(@Param("projectNo") String projectNo,
            @Param("mchntCd") String mchntCd, @Param("stageNo") String stageNo);

    int updateByPrimaryKey(ProjectStage projectStage);

    int updateByStageNo(ProjectStage projectStage);

    void deleteByProjectNoAndMchntCd(@Param("projectNo") String projectNo, @Param("mchntCd") String mchntCd);

    int deleteByStageNo(@Param("projectNo") String projectNo, @Param("mchntCd") String mchntCd,
            @Param("stageNos") Set<String> stageNos);

    List<ProjectStage> selectByMchntCdAndProjectNo(@Param("mchntCd") String mchntCd,
            @Param("projectNo") String projectNo);

    /**
     * 支付回调更新项目阶段信息
     *
     * @param projectStage
     * @return
     */
    int updateStageForTxn(ProjectStage projectStage);

    /**
     * 退款回调更新项目阶段信息
     *
     * @param projectStage
     * @return
     */
    int updateStageForRefund(ProjectStage projectStage);

    int startStageForTxn(@Param("mchntCd") String mchntCd,
            @Param("projectNo") String projectNo, @Param("stageNo") String stageNo);

    int updateLock(@Param("mchntCd") String mchntCd,
            @Param("projectNo") String projectNo, @Param("stageNo") String stageNo);

    int updateUnLock(@Param("mchntCd") String mchntCd,
            @Param("projectNo") String projectNo, @Param("stageNo") String stageNo);

    int batchUpdateLock(@Param("mchntCd") String mchntCd,
            @Param("projectNo") String projectNo, @Param("stageNos") List<String> stageNos);

    int batchUpdateUnLock(@Param("mchntCd") String mchntCd,
            @Param("projectNo") String projectNo, @Param("stageNos") List<String> stageNos);

    int updateStageForTxnWithoutUnLock(ProjectStage projectStage);

}
