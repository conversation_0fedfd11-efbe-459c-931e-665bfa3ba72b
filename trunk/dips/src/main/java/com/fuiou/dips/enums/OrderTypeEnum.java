package com.fuiou.dips.enums;

import com.fuiou.dips.utils.StringUtil;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description 订单类型枚举
 * @Date 2023/8/17 0017 15:10
 */
public enum OrderTypeEnum {

    UNKNOW("UNKNOW", "UNKNOW", "未知订单类型"),
    JSAPI("JSAPI", "WECHAT", "微信扫码支付"),
    FWC("FWC","ALIPAY","支付宝扫码支付"),
    DIGICCY("DIGICCY","DIGICCY","数字货币"),
    ALKB("ALKB","ALKB","阿里口碑"),
    WXZL("WXZL","WXZL","微信直连"),
    CASH("CASH","CASH","现金"),
    ;

    private String orderType;
    private String wposQueryType;
    private String desc;

    OrderTypeEnum(String orderType, String wposQueryType, String desc) {
        this.orderType = orderType;
        this.wposQueryType = wposQueryType;
        this.desc = desc;
    }

    public static String  getDescByOrderType(String orderType) {
        return Arrays.stream(values()).filter(e -> StringUtil.isNotBlank(orderType) && e.getOrderType().equals(orderType.trim()))
                .findFirst().map(OrderTypeEnum::getDesc).orElse(orderType);
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getWposQueryType() {
        return wposQueryType;
    }

    public void setWposQueryType(String wposQueryType) {
        this.wposQueryType = wposQueryType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
