package com.fuiou.dips.enums;

/***
 * <AUTHOR>
 * @Description 错误码枚举
 * @Date 2025/4/4 9:16
 **/
public enum ResponseCodeEnum {

    SUCCESS("0000", "成功"),
    EXCEPTION("9999", "系统异常"),
    DB_EXCEPTION("9998", "数据库操作异常"),
    RUNTIME_EXCEPTION("9997", "操作执行异常"),
    CHECK_SIGN_ERROR("9996", "验签失败"),


    PERMISSION_EXCEPTION("4003", "请求失败，请重试"),
    ROLE_PERMISSION_EXCEPTION("4004", "用户权限不足"),
    CLIENT_HTTP_METHOD_ERROR("4005", "客户端HTTP请求方法错误"),


    SIGN_ERROR("1001", "验签失败："),
    FIELD_EMPTY_ERROR("1002", "字段值不能为空"),

    FIELD_LENGTH_ERROR("1010", "字段长度超限："),
    PARAM_ERROR("1011", "参数错误"),
    TIME_FORMAT_ERROR("1018", "日期时间格式不正确"),

    APP_NOT_EXIST("2001", "appid不存在"),
    WECHAT_LOGIN_FAIL("2002", "微信授权登录失败"),
    ACCOUNT_OR_PWD_ERROR("2003", "账号或密码错误"),
    ACCOUNT_STATUS_ERROR("2004", "账号状态错误"),
    MCHNT_INFO_ERROR("2005", "商户信息错误"),
    ACCOUNT_HAS_NOT_LOGIN_ERROR("2006", "请重新登录"),
    LOGIN_FAIL("2007", "登录失败"),
    SEND_SMS_FAIL("2008", "短信发送失败"),
    SMS_CODE_INVALID("2009", "验证码已失效"),
    SMS_CODE_ERROR("2010", "验证码错误"),
    CAPTCHA_ERROR("2011", "安全验证错误"),
    USER_NOT_EXIST("2012", "无此用户信息"),
    CUSTOMER_NOT_EXIST("2013", "客户信息不存在"),
    ACCOUNT_TYPE_ERROR("2014", "账号类型错误"),
    ACCOUNT_NOT_EXIST("2015", "账号不存在"),
    MCHNT_ZXT_BUSI_INFO_ERROR("2015", "商户装修通业务状态错误"),
    TERM_NOT_EXIST("2016", "终端信息不存在"),
    TERM_OWNER_NOT_CURR_MCHNT("2017", "终端归属信息异常"),

    // 项目管理
    STORES_DO_NOT_PAYMENT_TIED("3001", "所有门店未绑定任何支付"),
    MCHNT_CD_EMPTY_ERROR("3002", "商户号不能为空"),
    PROJECT_NON_EXIST("3003", "项目不存在"),
    PROJECTS_IN_PROGRESS_CAN_BE_CLOSED("3004", "只有进行中的项目才能关闭"),
    PROJECT_LOCKED_AND_NON_EDITED("3005", "项目已锁定，无法编辑"),
    PROJECT_PAYED_AND_STORE_NON_MODIFIED("3006", "项目已收款，无法修改门店"),
    PROJECT_STAGE_NON_EMPTY("3007", "项目阶段不能为空"),
    PROJECT_STAGE_AMOUNTS_MUST_EQUAL_TOTAL_PROJECT_AMOUNTS("3008", "阶段金额总和必须等于项目总金额"),
    PROJECT_STAGE_ORDER_MUST_START_FROM_ONE_AND_INCREASE("3009", "阶段顺序必须从1开始递增且不重复"),
    PROJECT_PAYED_AND_AMT_NON_MODIFIED("3010", "项目已收款，金额无法修改"),
    PROJECT_STATUS_NOT_ALLOWED("3011", "当前项目状态不允许执行此操作，请确认项目状态或稍后尝试"),
    PROJECT_STATE_STATUS_NOT_ALLOWED("3011", "当前项目阶段状态不允许执行此操作，请确认项目阶段状态或稍后尝试"),
    CUSTOMER_EXIST_NON_ADD("3012", "新增失败，项目经理请选择自己创建的客户"),
    CUSTOMER_EXIST_NON_ADD_BY_NON_STORE("3012", "新增失败，门店用户，需选择自己门店下的客户"),
    CUSTOMER_EXIST_NON_ADD_BY_NON_MCHNT_CD("3012", "非当前商户下的客户，无法新增客户信息"),
    CUSTOMER_NON_EXIST("3012", "客户不存在"),
    PROJECT_STAGE_NON_EXIST("3013", "项目阶段不存在"),
    PROJECTS_NON_BIND_TERM("3014", "当前项目无可用台卡，请联系代理商绑定台卡"),
    STORES_NON_BIND_TERM("3015", "当前门店无可用台卡，请联系代理商绑定台卡"),
    PROJECT_STATE_AMT_MORE_THAN_ACTUAL_AMT("3016", "当前项目阶段应收金额大于实收金额"),
    PROJECT_AMT_MORE_THAN_ACTUAL_AMT("3017", "当前项目应收金额大于实收金额"),
    PROJECT_BELONG_TO_SHOP_ERROR("3018", "项目所属门店与当前终端所属不一致"),
    LOGIN_USER_NOT_STORES("3019", "当前账号没有门店信息"),
    PROJECT_STAGE_EDIT_FAILURE("3020", "项目阶段编辑失败"),
    PROJECT_STAGE_DELETE_FAILURE("3021", "项目阶段编辑时，删除失效阶段失败"),
    PROJECT_STAGE_UPDATE_FAILURE("3022", "项目阶段编辑时，更新阶段失败"),
    PROJECT_STAGE_NOT_EXIST("3023", "项目阶段不存在"),
    PROJECT_STAGE_AMOUNT_CHANGE_FAIL("3033", "项目增减金额错误，金额没变"),

    ORDER_ENCODSIGN_ERROR("5001", "创建订单时加密参数失败"),
    ORDER_CXML_ERROR("5002", "生成xml字符串失败"),
    ORDER_CREATE_ERROR("5003", "订单创建失败"),
    PAY_QR_CREATE_ERROR("5004", "支付二维码创建失败"),
    PAY_QR_FAILURE_ERROR("5005", "支付二维码已失效"),
    AES_ENCRYPTION_ERROR("5006", "AES加密异常"),
    AES_DECRYPTION_ERROR("5007", "AES解密异常"),
    QRCODE_PIC_CREATE_ERROR("5008", "二维码图片生成失败"),
    TRANS_FAILURE("5009", "交易失败"),

    ORDER_NON_EXIST("5101", "订单不存在"),
    ORDER_STATUS_ERROR("5102", "订单状态错误"),
    ORDER_BELONG_TO_SHOP_ERROR("5103", "订单所属门店与当前终端所属不一致"),
    ORDER_STATUS_EXIRE("5104", "订单已失效"),
    STAGE_NON_EXIST("5105", "阶段不存在"),
    ORDER_HAS_EXIST("5106", "订单已存在");


    /***
     * <AUTHOR>
     * @Description 格式说明
     *                  1xxx:参数类错误
     *                  4xxx:登录、权限
     *                  3xxx:终端相关错误
     * @Date 2025/4/9 10:41
     * @return
     **/
    private String code;
    private String msg;

    ResponseCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }


}
