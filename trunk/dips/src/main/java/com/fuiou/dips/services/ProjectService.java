package com.fuiou.dips.services;

import cn.hutool.core.util.IdUtil;
import com.fuiou.cacheCenter.term.TermCacheData;
import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.convert.ProjectConvertMapper;
import com.fuiou.dips.data.entity.CompensationStep;
import com.fuiou.dips.data.entity.StoreInfo;
import com.fuiou.dips.data.req.CustomerReq;
import com.fuiou.dips.data.req.EmpPageReq;
import com.fuiou.dips.data.req.ProjectReq;
import com.fuiou.dips.data.req.ProjectStageReq;
import com.fuiou.dips.data.resp.*;
import com.fuiou.dips.enums.EmployeeRoleTypeEnum;
import com.fuiou.dips.enums.LockFlagEnum;
import com.fuiou.dips.enums.ProjectEnum;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.context.ApplicationContextKeeper;
import com.fuiou.dips.framework.exception.FUApiAssert;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.persist.beans.*;
import com.fuiou.dips.persist.dipsdb.ProjectMapper;
import com.fuiou.dips.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.fuiou.dips.consts.ProjectConstant.*;

/**
 * 项目信息
 *
 * <AUTHOR>
 */
@Service
public class ProjectService {
    private static final Logger log = LoggerFactory.getLogger(ProjectService.class);

    @Resource
    private ProjectConvertMapper projectConvertMapper;
    @Resource
    private MchntCfgService mchntCfgService;
    @Resource
    private CfgdbService cfgdbService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectStageService projectStageService;
    @Resource
    private ProjectEmployeeService projectEmployeeService;
    @Resource
    private CustomerService customerService;
    @Resource
    private TermInfoService termInfoService;
    @Resource
    private ProjectCustomerService projectCustomerService;
    @Resource
    private ProjectStageChangeService projectStageChangeService;

    public PageRespBase<EmpInfoResp> selectEmpListByMchntCd(EmpPageReq employee) {
        // 获取所有数据
        List<EmpInfoResp> allEmpInfoResps = getEmpInfos(employee.getMchntCd());
        // 分页返回
        return CustomPageUtil.manualPaging(allEmpInfoResps, employee.getPage(), employee.getLimit());
    }

    public List<EmpInfoResp> getEmpInfos(String mchntCd) {
        List<Map<String, Object>> resultList = cfgdbService.executeCommonQueryListPrepare(
                GET_EMPLOYEE_LIST_BY_MCHNT_CD_SQL, "t_mchnt_acnt_login_inf 员工信息", mchntCd);
        return MapUtils.mapToEntityList(resultList, EmpInfoResp.class);
    }

    public List<StoreInfoResp> getStoresByMchntCd(String mchntCd) {
        FUApiAssert.isNotBlank(ResponseCodeEnum.MCHNT_CD_EMPTY_ERROR, mchntCd);
        // 1，查询商户配置，获取终端号,不存在就报错
        // 2025-5-26 逻辑去掉，不需要了
        // MchntCfg mchntCfg = mchntCfgService.getDetailByMchntCd(mchntCd);
        // FUApiAssert.isFalse(ResponseCodeEnum.STORES_DO_NOT_PAYMENT_TIED,
        //         mchntCfg == null || StringUtil.isBlank(mchntCfg.getDefaultTermId()));
        LoginResp loginToken = LoginConstat.getLoginToken();
        if (EmployeeRoleTypeEnum.STORE.getCode().equals(loginToken.getEmployeeRoleType()) ||
                EmployeeRoleTypeEnum.CASHIER.getCode().equals(loginToken.getEmployeeRoleType()))
        {
            // 2，门店账号，只能查看自己门店
            // 2025-5-26 从登录里面取
            List<StoreInfo> relateStoreList = Optional.ofNullable(loginToken.getRelateStoreList()).orElse(
                    new ArrayList<>());
            if (CollectionUtils.isEmpty(relateStoreList)) {
                return new ArrayList<>();
            }
            String[] storeIds = relateStoreList.stream().map(x -> x.getStoreId()).collect(Collectors.toList()).toArray(
                    new String[0]);
            return getStoreInfoByMchntCdAndStoreId(mchntCd, storeIds);
        }
        // 3. 查询商户配置，根据终端号获取门店信息，这里跟下面的数据重复了。
        // storeSet.addAll(queryStoreByMchntConfig(mchntCd, mchntCfg.getDefaultTermId()));
        // 4. 查询商户号关联的门店信息
        return new ArrayList<>(queryStoreByMchntCd(mchntCd));
    }

    /**
     * 根据商户配置查询门店信息
     *
     * @param mchntCd 商户号
     * @return 门店集合
     */
    private Set<StoreInfoResp> queryStoreByMchntConfig(String mchntCd, String termId) {
        Set<StoreInfoResp> storeSet = new HashSet<>();
        // 根据终端号获取门店id
        TermCacheData termCacheData = termInfoService.queryTermInfo(termId);
        if (termCacheData == null || StringUtils.isEmpty(termCacheData.getStoreId())) {
            return storeSet;
        }
        List<StoreInfoResp> stores = getStoreInfoByMchntCdAndStoreId(mchntCd, termCacheData.getStoreId());
        storeSet.addAll(stores);
        return storeSet;
    }

    private List<StoreInfoResp> getStoreInfoByMchntCdAndStoreId(String mchntCd, String... storeIds) {
        // 根据商户号+门店id,获取门店信息
        // 动态生成占位符
        String placeholders = String.join(",", Collections.nCopies(storeIds.length, "?"));

        // 创建参数列表，将mchntCd放在数组第0个位置
        String[] params = new String[storeIds.length + 1];
        params[0] = mchntCd;
        System.arraycopy(storeIds, 0, params, 1, storeIds.length);
        List<Map<String, Object>> result = cfgdbService.executeCommonQueryListPrepare(
                String.format(GET_STORE_LIST_BY_USER_ID_SQL, placeholders), "t_bt_cashier_inf门店信息", params);
        return MapUtils.mapToEntityList(result, StoreInfoResp.class);
    }

    /**
     * 根据商户号查询门店信息
     *
     * @param mchntCd 商户号
     * @return 门店集合
     */
    public List<StoreInfoResp> queryStoreByMchntCd(String mchntCd) {
        List<Map<String, Object>> resultList = cfgdbService.executeCommonQueryListPrepare(
                GET_STORE_LIST_BY_MCHNT_CD_SQL, "t_bt_cashier_inf门店信息", mchntCd);
        return MapUtils.mapToEntityList(resultList, StoreInfoResp.class);
    }

    public String add(ProjectReq projectReq) throws Exception {
        // 校验金额
        validateStageAmounts(projectReq);
        // 1. 项目编号
        String projectNo = generateProjectNo();
        try {
            // 2. 保存项目客户
            saveProjectCustomer(projectReq, projectNo);
            // 3. 保存项目成员
            saveProjectEmployees(projectReq, projectNo);
            // 4. 插入项目
            Project project = projectConvertMapper.projectReqToProject(projectReq);
            project.setProjectNo(projectNo);
            projectMapper.insert(project);
            // 5. 保存项目阶段
            saveProjectStages(projectReq.getMchntCd(), projectNo, project.getRowId(), projectReq.getStageReqs());
            return projectNo;
        } catch (Exception e) {
            // 执行失败，补偿操作
            compensateAddOperation(projectNo, projectReq.getMchntCd());
            throw e;
        }
    }

    public void compensateAddOperation(String projectNo, String mchntCd) {
        LogWriter.info(String.format("开始执行补偿操作，项目编号：%s，商户号：%s", projectNo, mchntCd));
        List<CompensationStep> steps = Arrays.asList(new CompensationStep("项目阶段数据新增回滚",
                        () -> projectStageService.deleteByProjectNoAndMchntCd(projectNo, mchntCd),
                        String.format("projectNo=%s,mchntCd=%s", projectNo, mchntCd)), new CompensationStep("项目数据新增回滚",
                        () -> projectMapper.deleteByProjectNoAndMchntCd(projectNo, mchntCd),
                        String.format("projectNo=%s,mchntCd=%s", projectNo, mchntCd)),
                new CompensationStep("项目成员数据新增回滚",
                        () -> projectEmployeeService.deleteByProjectNoAndMchntCd(projectNo, mchntCd),
                        String.format("projectNo=%s,mchntCd=%s", projectNo, mchntCd)),
                new CompensationStep("项目客户关联数据新增回滚",
                        () -> projectCustomerService.deleteByProjectNoAndMchntCd(projectNo, mchntCd),
                        String.format("projectNo=%s,mchntCd=%s", projectNo, mchntCd)));
        runCompensate(steps);
    }

    public void runCompensate(List<CompensationStep> steps) {
        steps.forEach(step -> {
            try {
                step.getOperation().run();
                LogWriter.info(String.format("补偿操作成功：%s ，参数：%s", step.getDescription(), step.getLogDesc()));
            } catch (Exception e) {
                LogWriter.error(
                        String.format("补偿操作失败：%s，参数：%s，异常：%s", step.getDescription(), step.getLogDesc(), e));
            }
        });
        LogWriter.info(String.format("补偿回滚操作全部完成！"));
    }

    private void saveProjectCustomer(ProjectReq projectReq, String projectNo) {
        Customer customer = projectConvertMapper.customerReqToCustomer(projectReq.getCustomerReq());
        String phone = customer.getPhone();
        if (customer.getRowId() != null) {
            Customer tempCustomer = customerService.selectByPrimaryKey(customer.getRowId());
            FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NON_EXIST, tempCustomer);
            validCustomer(projectReq, tempCustomer);
            phone = tempCustomer.getPhone();
        } else {
            // 保存客户信息（商户号+手机号是唯一的）
            customer.setStoreId(Optional.ofNullable(projectReq.getStoreId()).orElse(""));
            customer.setMchntCd(projectReq.getMchntCd());
            customer.setLoginId(LoginConstat.getLoginToken().getLoginId());
            Long customerId = customerService.insertByNonExist(customer);
            LogWriter.info("客户信息处理完成，客户ID：" + customerId + "，手机号：" + phone);
        }
        // 保存项目客户关联表
        projectCustomerService.insert(projectNo, projectReq.getMchntCd(), phone);
        LogWriter.info("项目客户关联插入成功，项目编号：" + projectNo + "，手机号：" + phone);
    }

    private void validCustomer(ProjectReq projectReq, Customer tempCustomer) {
        FUApiAssert.equals(ResponseCodeEnum.CUSTOMER_EXIST_NON_ADD_BY_NON_MCHNT_CD, tempCustomer.getMchntCd(),
                projectReq.getMchntCd());

        LoginResp loginToken = LoginConstat.getLoginToken();
        List<StoreInfo> relateStoreList = Optional.ofNullable(loginToken.getRelateStoreList()).orElse(
                new ArrayList<>());
        String relateStoreUserId = loginToken.getRelateStoreUserId();
        if (StringUtil.isNotBlank(relateStoreUserId)) {
            relateStoreList.add(new StoreInfo(relateStoreUserId));
        }

        if (EmployeeRoleTypeEnum.CASHIER.getCode().equals(loginToken.getEmployeeRoleType()) ||
                EmployeeRoleTypeEnum.STORE.getCode().equals(loginToken.getEmployeeRoleType()))
        {
            // 收银员和门店账号，看自己的门店客户
            if (CollectionUtils.isNotEmpty(relateStoreList)) {
                boolean storeExists = relateStoreList.stream().anyMatch(
                        storeInfo -> storeInfo.getStoreId().equals(tempCustomer.getStoreId()));
                FUApiAssert.isTrue(ResponseCodeEnum.CUSTOMER_EXIST_NON_ADD_BY_NON_STORE, storeExists);
            }
        } else if (EmployeeRoleTypeEnum.DECORATION_MANAGER.getCode().equals(loginToken.getEmployeeRoleType())) {
            // 项目经理，看自己的客户
            FUApiAssert.equals(ResponseCodeEnum.CUSTOMER_EXIST_NON_ADD, tempCustomer.getLoginId(),
                    loginToken.getLoginId());
        }
    }

    private void saveProjectEmployees(ProjectReq projectReq, String projectNo) {
        // 收集所有项目成员到列表中
        List<ProjectEmployee> employeeList = projectReq.getEmployeeList().stream().map(employee -> {
            // 项目成员关联维护
            ProjectEmployee projectEmployee = new ProjectEmployee();
            projectEmployee.setProjectNo(projectNo);
            projectEmployee.setMchntCd(projectReq.getMchntCd());
            projectEmployee.setEmployeeLoginId(employee.getLoginId());
            return projectEmployee;
        }).collect(Collectors.toList());

        // 批量插入
        if (!employeeList.isEmpty()) {
            projectEmployeeService.batchInsert(employeeList);
        }
    }

    private void saveProjectStages(String mchntCd, String projectNo, Long projectId, List<ProjectStageReq> stageReqs) {
        // 调用ProjectStageService的批量保存方法
        projectStageService.batchSaveStages(projectNo, mchntCd,
                projectConvertMapper.projectStageReqToProjectStageList(stageReqs));

        // 设置第一个阶段为当前阶段
        ProjectStage stage = projectStageService.selectFirstStageByProjectNoAndMchntCd(projectNo, mchntCd);
        if (stage != null) {
            Project project = new Project();
            project.setRowId(projectId);
            project.setCurrentStageNo(stage.getStageNo());
            project.setProjectSt(ProjectEnum.ProjectStEnum.ONGOING.getState());
            projectMapper.updateByPrimaryKey(project);
        }
    }

    // 生成项目编号
    private String generateProjectNo() {
        return "ZXT" + IdUtil.getSnowflakeNextIdStr();
    }

    // 验证各阶段金额总和是否等于项目总金额
    private void validateStageAmounts(ProjectReq projectReq) throws Exception {
        FUApiAssert.notEmpty(ResponseCodeEnum.PROJECT_STAGE_NON_EMPTY, projectReq.getStageReqs());
        // 计算所有阶段金额总和
        BigDecimal stageAmountSum = projectReq.getStageReqs().stream().map(ProjectStageReq::getStageAmt).filter(
                Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 比较阶段金额总和与项目总金额
        FUApiAssert.notNull("3098", "项目总金额不能为空", projectReq.getProjectAmt());
        FUApiAssert.isFalse("3099",
                "各阶段金额总和(" + stageAmountSum + ")与项目总金额(" + projectReq.getProjectAmt() + ")不一致",
                stageAmountSum.compareTo(projectReq.getProjectAmt()) != 0);
    }

    public ProjectResp getDetailByProjectNoAndMchntCd(String projectNo, String mchntCd) throws Exception {
        // 1. 查询项目基本信息
        Project project = queryProjectByProjectNoAndMchntCd(projectNo, mchntCd);

        // 2. 构建返回对象
        ProjectResp detail = projectConvertMapper.projectToProjectResp(project);

        // 3. 加载门店列表
        detail.setStoreInfos(getStoresByMchntCd(mchntCd));

        // 4. 查询客户
        detail.setCustomerResp(getCustomerResp(projectNo, mchntCd));

        // 5. 查询项目成员
        List<ProjectEmployee> projectEmployees = projectEmployeeService.selectByProjectNoAndMchntCd(projectNo, mchntCd);
        detail.setProjectEmployeeResps(projectConvertMapper.projectEmployeeToProjectEmployeeRespList(projectEmployees));

        // 6. 员工列表
        detail.setEmployeeList(getEmpInfos(mchntCd));

        // 7. 查询项目阶段
        List<ProjectStage> projectStages = projectStageService.selectByProjectNoAndMchntCd(projectNo, mchntCd);
        detail.setStageResps(projectConvertMapper.projectStageToProjectStageRespList(projectStages));

        PairMap<BigDecimal, BigDecimal> amtPairMap = pairAmt(projectStages, project.getProjectAmt());
        // 8. 未收款 = 总金额-已收+已退
        detail.setPendingAmt(amtPairMap.getLeft());
        // 9. 已收款 = 已收-已退
        detail.setActualAmt(amtPairMap.getRight());

        // 10. 变更记录
        List<ProjectStageChange> projectStageChanges = projectStageChangeService.selectByProjectNoAndMchntCd(projectNo,
                mchntCd);
        detail.setProjectStageChangeResps(
                projectConvertMapper.projectStageChangeToProjectStageChangeRespList(projectStageChanges));
        return detail;
    }

    private PairMap<BigDecimal, BigDecimal> pairAmt(List<ProjectStage> projectStages, BigDecimal projectAmt) {
        // 已收款
        BigDecimal actualAmt = projectStages.stream().map(ProjectStage::getStageActualAmt).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 已退款
        BigDecimal refundAmt = projectStages.stream().map(ProjectStage::getRefundAmt).filter(Objects::nonNull).reduce(
                BigDecimal.ZERO, BigDecimal::add);

        // 未收款 = 总金额-已收+已退
        // 已收款 = 已收-已退
        return PairMap.of(projectAmt.subtract(actualAmt).add(refundAmt), actualAmt.subtract(refundAmt));
    }

    private CustomerResp getCustomerResp(String projectNo, String mchntCd) {
        ProjectCustomer projectCustomer = projectCustomerService.selectByProjectNoAndMchntCdAndPhone(projectNo, mchntCd,
                null);
        CustomerResp customerResp = customerService.checkCustomerExists(mchntCd, null, projectCustomer.getPhone());
        return customerResp;
    }

    /**
     * 可编辑内容：门店（未收款可切换门店）、客户手机号、项目成员（可增减）、备注，客户姓名无法编辑
     */
    public int edit(ProjectReq projectReq) throws Exception {
        Project project = queryProjectByProjectNoAndMchntCd(projectReq.getProjectNo(), projectReq.getMchntCd());
        // 校验是否可编辑
        vaildProjectEditPermission(projectReq, project);
        // 1. 更新项目基本信息（门店、备注）
        int result = updateProjectStoreIdAndRemark(project.getRowId(), projectReq.getStoreId(),
                projectReq.getReserved1(), projectReq.getRemark());
        // 2. 更新项目成员（有变更就全量传过来，否则就传空）
        updateProjectEmployee(projectReq);
        // 3. 更新项目客户信息（手机号）
        updateProjectPhone(projectReq.getProjectNo(), projectReq.getMchntCd(), projectReq.getCustomerReq());
        return result;
    }

    private int updateProjectStoreIdAndRemark(Long rowId, String storeId, String reserved1, String remark) {
        Project updateProject = new Project();
        updateProject.setRowId(rowId);
        updateProject.setUpdateTime(new Date());
        if (StringUtils.hasText(storeId)) {
            updateProject.setStoreId(storeId);
        }
        if (StringUtils.hasText(reserved1)) {
            updateProject.setReserved1(reserved1);
        }
        if (StringUtils.hasText(remark)) {
            updateProject.setRemark(remark);
        }
        int result = projectMapper.updateByPrimaryKey(updateProject);
        return result;
    }

    public void updateProjectAmt(Project project, String newProjectSt, BigDecimal projectAmt) {
        Project updateProject = new Project();
        updateProject.setRowId(project.getRowId());
        updateProject.setProjectAmt(projectAmt);
        updateProject.setUpdateTime(new Date());
        // 如果项目状态为"已完成"，则更新为"进行中"
        if (ProjectEnum.ProjectStEnum.COMPLETED.getState().equals(project.getProjectSt()) && StringUtil.isNotBlank(
                newProjectSt))
        {
            // 如果当前阶段已完成，则将下一个未开始的阶段编号赋给项目 currentStageNo
            String nextStageNo = findNextUnstartedStageNo(project.getProjectNo(), project.getMchntCd());
            if (StringUtil.isNotBlank(nextStageNo)) {
                updateProject.setCurrentStageNo(nextStageNo);
                updateProject.setProjectSt(newProjectSt);
            }
        }
        projectMapper.updateByPrimaryKey(updateProject);
    }

    public void updateProjectAmt(String projectNo, String mchntCd, BigDecimal projectAmt) {
        projectMapper.updateByProjectNoAndMchntCd(projectNo, mchntCd, projectAmt);
    }

    /**
     * 查找下一个未开始的阶段编号
     *
     * @param projectNo 项目编号
     * @param mchntCd   商户号
     * @return 下一个未开始阶段的编号，如果没有则返回null
     */
    private String findNextUnstartedStageNo(String projectNo, String mchntCd) {
        // 查询项目的所有阶段信息，按阶段顺序排序
        List<ProjectStage> stages = projectStageService.selectByProjectNoAndMchntCd(projectNo, mchntCd);

        // 查找第一个未开始的阶段
        return stages.stream()
                .filter(stage -> ProjectEnum.StageStEnum.NON_START.getState().equals(stage.getStageSt()))
                .sorted(Comparator.comparing(ProjectStage::getStageOrder))
                .map(ProjectStage::getStageNo)
                .findFirst()
                .orElse(null);
    }

    private void updateProjectEmployee(ProjectReq projectReq) {
        if (CollectionUtils.isEmpty(projectReq.getEmployeeList())) {
            return;
        }
        // 先删除旧成员
        projectEmployeeService.deleteByProjectNoAndMchntCd(projectReq.getProjectNo(), projectReq.getMchntCd());

        List<ProjectEmployee> employeeList = projectReq.getEmployeeList().stream().map(employee -> {
            ProjectEmployee projectEmployee = new ProjectEmployee();
            projectEmployee.setProjectNo(projectReq.getProjectNo());
            projectEmployee.setMchntCd(projectReq.getMchntCd());
            projectEmployee.setEmployeeLoginId(employee.getLoginId());
            return projectEmployee;
        }).collect(Collectors.toList());
        // 批量插入新成员
        projectEmployeeService.batchInsert(employeeList);
    }

    private void updateProjectPhone(String projectNo, String mchntCd, CustomerReq customerReq) {
        Customer customer = projectConvertMapper.customerReqToCustomer(customerReq);
        if (!StringUtils.hasText(customer.getPhone())) {
            return;
        }
        // 1. 根据rowId查询客户信息，获取原手机号
        Customer oldCustomer = customerService.selectByPrimaryKey(customer.getRowId());
        if (oldCustomer == null || !oldCustomer.getMchntCd().equals(mchntCd)) {
            return;
        }
        // 2. 更新客户表中的手机号
        customerService.updateByPrimaryKey(customer.getRowId(), customer.getPhone());
        // 3. 根据原手机号、商户号和项目编号查询项目客户关联记录
        ProjectCustomer projectCustomer = projectCustomerService.selectByProjectNoAndMchntCdAndPhone(projectNo, mchntCd,
                oldCustomer.getPhone());
        if (projectCustomer != null) {
            // 4. 更新项目客户关联表中的手机号
            projectCustomerService.updatePhoneByPrimaryKey(projectCustomer.getRowId(), customer.getPhone());
        }
    }

    private void vaildProjectEditPermission(ProjectReq projectReq, Project project) {
        // 1. 验证项目是否可编辑（已锁定）
        FUApiAssert.equals(ResponseCodeEnum.PROJECT_LOCKED_AND_NON_EDITED, project.getLockFlag(),
                LockFlagEnum.LOCK.getState());

        // 1.1. 验证项目是否可编辑（进行中）
        FUApiAssert.equals(ResponseCodeEnum.PROJECTS_IN_PROGRESS_CAN_BE_CLOSED, project.getProjectSt(),
                ProjectEnum.ProjectStEnum.ONGOING.getState());

        // 2. 检查门店是否可修改（未收款可切换门店）
        if (projectReq.getStoreId() == null || projectReq.getStoreId().equals(project.getStoreId())) {
            return;
        }
        // 检查第一阶段是否已收款
        ProjectStage firstStage = projectStageService.selectFirstStageByProjectNoAndMchntCd(projectReq.getProjectNo(),
                projectReq.getMchntCd());
        FUApiAssert.isFalse(ResponseCodeEnum.PROJECT_PAYED_AND_STORE_NON_MODIFIED,
                firstStage != null && firstStage.getStageActualAmt() != null &&
                        firstStage.getStageActualAmt().compareTo(BigDecimal.ZERO) > 0);

    }

    public ProjectResp getProjectStatus(String projectNo, String mchntCd) throws Exception {
        // 1. 查询项目基本信息
        Project project = queryProjectByProjectNoAndMchntCd(projectNo, mchntCd);

        ProjectResp status = new ProjectResp();
        // 2. 项目状态
        status.setProjectSt(project.getProjectSt());
        status.setPaymentStatus(ProjectEnum.PaymentStatusEnum.NON_PAY.getState()); // 默认0，未收款

        // 3. 收款状态（判断当前阶段是否1，不是1就返回已收款，是1就判断项目阶段表，第1阶段是否已收款）
        List<ProjectStage> stages = projectStageService.selectByProjectNoAndMchntCd(projectNo, mchntCd);
        stages.stream().filter(s -> s.getStageNo().equals(project.getCurrentStageNo())).findAny().ifPresent(s -> {
            if (s.getStageOrder() != ProjectEnum.INIT_STAGE_ORDER) {
                status.setPaymentStatus(ProjectEnum.PaymentStatusEnum.PAYED.getState());
            } else {
                if (s.getStageActualAmt() != null && s.getStageActualAmt().compareTo(BigDecimal.ZERO) > 0) {
                    status.setPaymentStatus(ProjectEnum.PaymentStatusEnum.PAYED.getState());
                }
            }
        });
        return status;
    }

    @LogAnnotation("项目信息-查询项目详情")
    public Project queryProjectByProjectNoAndMchntCd(String projectNo, String mchntCd) {
        Project project = projectMapper.selectByProjectNoAndMchntCd(projectNo, mchntCd);
        FUApiAssert.notNull(ResponseCodeEnum.PROJECT_NON_EXIST, project);
        return project;
    }

    @LogAnnotation("项目信息-关闭项目")
    public int closeProject(String projectNo, String mchntCd) {
        Project project = queryProjectByProjectNoAndMchntCd(projectNo, mchntCd);
        FUApiAssert.equals(ResponseCodeEnum.PROJECTS_IN_PROGRESS_CAN_BE_CLOSED, project.getProjectSt(),
                ProjectEnum.ProjectStEnum.ONGOING.getState());
        FUApiAssert.equals(ResponseCodeEnum.PROJECT_LOCKED_AND_NON_EDITED, project.getLockFlag(),
                LockFlagEnum.UNLOCK.getState());
        // 更新项目状态为已关闭
        return projectMapper.updateProjectStatus(projectNo, mchntCd, ProjectEnum.ProjectStEnum.CLOSED.getState());
    }

    public void checkStoreByMchntCd(String mchntCd, String storeId) {
        /**
         * 1、在家装通新增项目点击“下一步”按钮时，以下有两种情况
         * 1）商户下没有门店，判断商户下是否有已绑定可用台卡，若无则提示“当前项目无可用台卡，请联系代理商绑定台卡”，但不影响项目的创建仅提示。
         * 2）商户下有门店，则判断项目创建的门店下是否有已绑定可用台卡，若无则提示“当前门店无可用台卡，请联系代理商绑定台卡”，但不影响项目的创建仅提示。
         */
        MchntCfg mchntCfg = mchntCfgService.getDetailByMchntCd(mchntCd);
        if (StringUtil.isBlank(storeId)) {
            boolean flag = mchntCfg == null || StringUtil.isBlank(mchntCfg.getDefaultTermId());
            FUApiAssert.isFalse(ResponseCodeEnum.PROJECTS_NON_BIND_TERM, flag);
        } else {
            TermCacheData termCacheData = termInfoService.queryTermInfo(mchntCfg.getDefaultTermId());
            boolean flag = termCacheData == null || StringUtil.isBlank(termCacheData.getStoreId()) ||
                    !StringUtil.equals(termCacheData.getStoreId(), storeId);
            FUApiAssert.isFalse(ResponseCodeEnum.STORES_NON_BIND_TERM, flag);
        }
    }


    public static void validProject(Project projectEntry) {
        if (projectEntry == null) {
            throw new FUException(ResponseCodeEnum.PROJECT_NON_EXIST);
        }
        if (!ProjectEnum.ProjectStEnum.ONGOING.getState().equals(projectEntry.getProjectSt())) {
            log.info("项目状态为：{}，项目锁定状态为：{}，不允许执行此操作", projectEntry.getProjectSt(),
                    projectEntry.getLockFlag());
            throw new FUException(ResponseCodeEnum.PROJECT_STATUS_NOT_ALLOWED);
        }
        if (!LockFlagEnum.UNLOCK.getState().equals(projectEntry.getLockFlag())) {
            log.info("项目状态为：{}，项目锁定状态为：{}，不允许执行此操作", projectEntry.getProjectSt(),
                    projectEntry.getLockFlag());
            throw new FUException(ResponseCodeEnum.PROJECT_STATUS_NOT_ALLOWED);
        }
    }

    @LogAnnotation("项目信息-查询项目详情")
    public Project queryProject(String projectNo, String mchntCd) throws Exception {
        Project projectEntry = ApplicationContextKeeper.getBean(this.getClass()).queryProjectByProjectNoAndMchntCd(
                projectNo, mchntCd);
        ProjectService.validProject(projectEntry);
        return projectEntry;
    }


    @LogAnnotation("项目信息-更新项目信息")
    public int updateProjectForTxn(Project project) {
        return projectMapper.updateProjectForTxn(project);
    }
}
