package com.fuiou.dips.services;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.convert.ProjectConvertMapper;
import com.fuiou.dips.data.entity.StoreInfo;
import com.fuiou.dips.data.req.CustomerFollowReq;
import com.fuiou.dips.data.req.CustomerInfoReq;
import com.fuiou.dips.data.req.CustomerPageReq;
import com.fuiou.dips.data.req.CustomerQueryReq;
import com.fuiou.dips.data.resp.CustomerResp;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.data.resp.PageRespBase;
import com.fuiou.dips.enums.EmployeeRoleTypeEnum;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.exception.FUApiAssert;
import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.persist.beans.Customer;
import com.fuiou.dips.persist.beans.DipsCustomerFollow;
import com.fuiou.dips.persist.beans.DipsUserInf;
import com.fuiou.dips.persist.dipsdb.CustomerMapper;
import com.fuiou.dips.persist.dipsdb.DipsCustomerFollowMapper;
import com.fuiou.dips.persist.dipsdb.DipsUserInfMapper;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.Util;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_dips_customer(装修通客户表)】的数据库操作Mapper
 * @createDate 2025-05-06 15:32:30
 * @Entity com.fuiou.dips.model.DipsCustomer
 */
@Service
public class CustomerService {
    @Resource
    private CustomerMapper customerMapper;
    @Resource
    private ProjectConvertMapper projectConvertMapper;
    @Resource
    private DipsCustomerFollowMapper dipsCustomerFollowMapper;
    @Resource
    private DipsUserInfMapper dipsUserInfMapper;

    public List<CustomerResp> listPage(CustomerQueryReq customerQueryReq) {
        LoginResp loginToken = LoginConstat.getLoginToken();
        if (EmployeeRoleTypeEnum.DECORATION_MANAGER.getCode().equals(loginToken.getEmployeeRoleType())) {
            customerQueryReq.setLoginId(loginToken.getLoginId());
        } else if (EmployeeRoleTypeEnum.CASHIER.getCode().equals(loginToken.getEmployeeRoleType()) ||
                EmployeeRoleTypeEnum.STORE.getCode().equals(loginToken.getEmployeeRoleType()))
        {
            List<StoreInfo> relateStoreList = loginToken.getRelateStoreList();
            if (CollUtil.isNotEmpty(relateStoreList)) {
                List<String> storeIds = new ArrayList<>();
                for (StoreInfo storeInfo : relateStoreList) {
                    storeIds.add(storeInfo.getStoreId());
                }
                customerQueryReq.setStoreIds(storeIds);
            }
        }
        // 去掉分页
        // PageHelper.startPage(customerPageReq.getPage(), customerPageReq.getLimit());
        List<Customer> list = customerMapper.selectList(customerQueryReq);
        return projectConvertMapper.customerListToCustomerRespList(list);
    }

    /**
     * 【所有角色】
     * 我的客户:我创建的客户
     * 【项目经理】
     * 全部客户:我加入团队的客户
     * 【超管、老板】
     * 全部客户:商户下的所有客户【
     * 门店】
     * 全部客户:门店下的所有客户
     *
     * @param customerPageReq
     * @return
     */
    public PageRespBase<CustomerResp> allListPage(CustomerPageReq customerPageReq) {
        LoginResp loginToken = LoginConstat.getLoginToken();
        if ("1".equals(customerPageReq.getSearchType())) {
            customerPageReq.setLoginId(loginToken.getLoginId());
        } else {
            if (EmployeeRoleTypeEnum.DECORATION_MANAGER.getCode().equals(loginToken.getEmployeeRoleType())) {
                List<String> phones = customerMapper.selectCustomerPhonesByMchntAndEmployee(
                        customerPageReq.getMchntCd(), loginToken.getLoginId());
                customerPageReq.setPhones(phones);
                customerPageReq.setLoginId(loginToken.getLoginId());
            } else if (EmployeeRoleTypeEnum.BOSS.getCode().equals(loginToken.getEmployeeRoleType()) ||
                    EmployeeRoleTypeEnum.SUPER_ADMIN.getCode().equals(loginToken.getEmployeeRoleType()))
            {
                customerPageReq.setStoreId(null);
            } else {
                List<StoreInfo> relateStoreList = loginToken.getRelateStoreList();
                if (CollUtil.isNotEmpty(relateStoreList)) {
                    List<String> storeIds = new ArrayList<>();
                    for (StoreInfo storeInfo : relateStoreList) {
                        storeIds.add(storeInfo.getStoreId());
                    }
                    customerPageReq.setStoreIds(storeIds);
                }
            }
        }
        PageHelper.startPage(customerPageReq.getPage(), customerPageReq.getLimit());
        List<Customer> list = customerMapper.selectAllList(customerPageReq);
        return new PageRespBase<>(new PageInfo<>(projectConvertMapper.customerListToCustomerRespList(list)));
    }

    /**
     * 查询客户详情（包括客户信息和跟进记录）
     */
    public CustomerResp getCustomerDetail(Long customerRowId) {
        // 查询客户基本信息
        Customer customer = customerMapper.selectByPrimaryKey(customerRowId);
        FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NON_EXIST, customer);
        CustomerResp customerResp = projectConvertMapper.customerToCustomerResp(customer);
        if (StrUtil.isNotBlank(customer.getLoginId())) {
            DipsUserInf dipsUserInf = dipsUserInfMapper.selectByLoginIdAndType(customer.getLoginId(), null);
            customerResp.setCreateBy(dipsUserInf.getFullName());
        }

        // 查询跟进记录
        List<DipsCustomerFollow> followList = dipsCustomerFollowMapper.
                selectByMchntCdAndPhone(customer.getMchntCd(), customer.getPhone());
        if (CollUtil.isNotEmpty(followList)) {
            DipsCustomerFollow follow = followList.get(0);
            customerResp.setFollowTime(follow.getCreateTime());
            customerResp.setFollowMan(follow.getFollowMan());
            customerResp.setFollowRemark(follow.getRemark());
        }
        return customerResp;
    }

    /**
     * 新增客户及跟进记录
     *
     * @param customerFollowReq 客户信息请求参数
     * @return 新增后的客户对象
     */
    public DipsCustomerFollow saveCustomerWithFollow(CustomerFollowReq customerFollowReq) {
        Long customerRowId = customerFollowReq.getCustomerRowId();
        Customer customer = customerMapper.selectByPrimaryKey(customerRowId);
        FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NOT_EXIST, customer == null);
        customer.setFollowTime(DateUtil.parse(customerFollowReq.getFollowDate()));
        int i = customerMapper.updateByPrimaryKey(customer);
        FUApiAssert.isTrue(ResponseCodeEnum.DB_EXCEPTION, i == 1);

        LoginResp loginToken = LoginConstat.getLoginToken();
        DipsCustomerFollow dipsCustomerFollow = new DipsCustomerFollow();
        dipsCustomerFollow.setLogNo(Util.generateRecordNo());
        dipsCustomerFollow.setFollowMan(StrUtil.blankToDefault(loginToken.getFullName(), loginToken.getLoginId()));
        dipsCustomerFollow.setCreater(dipsCustomerFollow.getFollowMan());
        dipsCustomerFollow.setMchntCd(customer.getMchntCd());
        dipsCustomerFollow.setPhone(customer.getPhone());
        dipsCustomerFollow.setReserved1(customer.getCustomerName());
        dipsCustomerFollow.setRemark(customerFollowReq.getRemark());

        int i1 = dipsCustomerFollowMapper.insertSelective(dipsCustomerFollow);
        FUApiAssert.isTrue(ResponseCodeEnum.DB_EXCEPTION, i1 == 1);
        return dipsCustomerFollow;
    }

    /**
     * 根据客户ID查询跟进记录列表
     *
     * @param customerRowId 客户主键ID
     * @return 跟进记录响应列表
     */
    public List<DipsCustomerFollow> getFollowListByCustomerId(Long customerRowId) {
        Customer customer = customerMapper.selectByPrimaryKey(customerRowId);
        FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NOT_EXIST, customer == null);
        return dipsCustomerFollowMapper.selectByMchntCdAndPhone(customer.getMchntCd(), customer.getPhone());

    }

    /**
     * 新增客户
     *
     * @param customerInfoReq
     */
    public Customer saveCustomer(CustomerInfoReq customerInfoReq) {
        Customer customer = new Customer();
        customer.setStoreId(customerInfoReq.getStoreId());
        LoginResp loginToken = LoginConstat.getLoginToken();
        customer.setPhone(customerInfoReq.getPhone());
        customer.setMchntCd(loginToken.getMchntInfo().getMchntCd());
        customer.setLoginId(loginToken.getLoginId());
        customer.setCustomerName(customerInfoReq.getCustomerName());
        customer.setCustomerSource(customerInfoReq.getCustomerSource());
        customer.setRemark(customerInfoReq.getRemark());
        Long customerId = persistCustomer(customer);
        LogWriter.info("客户信息处理完成，客户ID：" + customerId + "，手机号：" + customerInfoReq.getPhone());
        return customer;
    }

    /**
     * 修改客户
     *
     * @param customerInfoReq
     */
    public Customer updCustomer(CustomerInfoReq customerInfoReq) {
        Customer customer1 = customerMapper.selectByPrimaryKey(customerInfoReq.getRowId());
        FUApiAssert.notNull(ResponseCodeEnum.CUSTOMER_NOT_EXIST, customer1);
        LoginResp loginToken = LoginConstat.getLoginToken();
        boolean limitRoleFlag = ListUtil.toList(EmployeeRoleTypeEnum.CASHIER.getCode(),
                EmployeeRoleTypeEnum.STORE.getCode()).contains(loginToken.getEmployeeRoleType());
        //门店、收银员可以看到该门店下客户信息，但无法修改
        FUApiAssert.isTrue(ResponseCodeEnum.ACCOUNT_TYPE_ERROR, limitRoleFlag);

        Customer customer = new Customer();
        customer.setRowId(customerInfoReq.getRowId());
        customer.setStoreId(customerInfoReq.getStoreId());
        customer.setPhone(customerInfoReq.getPhone());
        customer.setLoginId(loginToken.getLoginId());
        customer.setCustomerName(customerInfoReq.getCustomerName());
        customer.setCustomerSource(customerInfoReq.getCustomerSource());
        customer.setRemark(customerInfoReq.getRemark());
        Long customerId = persistCustomer(customer);
        LogWriter.info("客户信息处理完成，客户ID：" + customerId + "，手机号：" + customerInfoReq.getPhone());
        return customer;
    }

    /**
     * 删除客户跟进记录
     */
    public void deleteCustomerFollow(Long followId) {
        dipsCustomerFollowMapper.deleteByPrimaryKey(followId);
    }

    public Long persistCustomer(Customer customer) {
        // 检查客户是否已存在（商户号+手机号是唯一的）
        Customer existingCustomer = customerMapper.selectByPhoneAndMchntCd(customer.getMchntCd(), null,
                customer.getPhone());
        if (existingCustomer == null) {
            // 客户不存在，插入新客户
            customerMapper.insert(customer);
            return customer.getRowId();
        } else {
            customerMapper.updateByPrimaryKey(customer);
        }
        return existingCustomer.getRowId();
    }

    public CustomerResp checkCustomerExists(String mchntCd, String storeId, String phone) {
        // 手机号必填，商户号有值时返回查出来的客户信息，没有值时仅判断手机号是否已存在
        Customer customer = customerMapper.selectByPhoneAndMchntCd(mchntCd, storeId, phone);
        return projectConvertMapper.customerToCustomerResp(customer);
    }

    public Long insertByNonExist(Customer customer) {
        // 检查客户是否已存在（商户号+手机号是唯一的）
        Customer existingCustomer = customerMapper.selectByPhoneAndMchntCd(customer.getMchntCd(), null,
                customer.getPhone());
        if (existingCustomer == null) {
            // 客户不存在，插入新客户
            customerMapper.insert(customer);
            return customer.getRowId();
        }
        return existingCustomer.getRowId();
    }

    public Customer selectByPrimaryKey(Long rowId) {
        return customerMapper.selectByPrimaryKey(rowId);
    }

    public int updateByPrimaryKey(Long rowId, String phone) {
        Customer customer = new Customer();
        customer.setRowId(rowId);
        customer.setPhone(phone);
        return customerMapper.updateByPrimaryKey(customer);
    }

    @LogAnnotation("客户信息-客户详情")
    public Customer selectByMchntCdAndPhone(String mchntCd, String phone) {
        return customerMapper.selectByPhoneAndMchntCd(mchntCd, null, phone);
    }

}
