package com.fuiou.dips.services;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.cacheCenter.mchnt.InsMchntCacheData;
import com.fuiou.cacheCenter.term.TermCacheData;
import com.fuiou.dips.consts.Constant;
import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.data.req.CreateOrderReq;
import com.fuiou.dips.data.req.OrderCallBackReq;
import com.fuiou.dips.data.req.PayPageInfoReq;
import com.fuiou.dips.data.req.PreCreateDataReq;
import com.fuiou.dips.data.resp.CreateOrderResp;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.data.resp.PayPageInfoResp;
import com.fuiou.dips.data.resp.PreCreateDataRes;
import com.fuiou.dips.enums.*;
import com.fuiou.dips.framework.exception.FUApiAssert;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.persist.beans.*;
import com.fuiou.dips.utils.*;
import com.fuiou.dips.utils.http.FuiouHttpPoster;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 交易订单服务
 *
 * <AUTHOR>
 */
@Service
public class OrderService {
    private static final Logger log = LoggerFactory.getLogger(OrderService.class);

    @Resource
    private TxnLogService txnLogService;
    @Resource
    private ProjectService projectService;


    @Resource
    private ProjectStageService projectStageService;
    @Resource
    private MchntCfgService mchntCfgService;
    @Resource
    private TermInfoService termInfoService;
    @Resource
    private TpayOrderService tpayOrderService;

    @Resource
    private MchntService mchntService;
    @Resource
    private CallBackPayService callBackPayService;
    @Resource
    private MsgService msgService;

    /**
     * @param payPageInfoReq :
     * @Description:
     * @return: com.fuiou.dips.data.resp.PayPageInfoResp
     * @Author: Joker
     * @Date: 2025/5/14 19:04
     */

    public PayPageInfoResp queryPayPageInfo(PayPageInfoReq payPageInfoReq) throws Exception {
        Project project = queryProject(payPageInfoReq.getProjectNo(), payPageInfoReq.getMchntCd());
        ProjectStage projectStage = queryProjectStage(payPageInfoReq.getProjectNo(), payPageInfoReq.getMchntCd(), payPageInfoReq.getStageNo());

        TermCacheData termDbData = queryTermInfo(payPageInfoReq.getMchntCd());
        if (!StringUtil.trimToEmpty(termDbData.getStoreId()).equals(StringUtil.trimToEmpty(project.getStoreId()))) {
            log.info("终端所属门店与当前项目所属门店不一致");
            throw new FUException(ResponseCodeEnum.TERM_OWNER_NOT_CURR_MCHNT);
        }
        PayPageInfoResp payPageInfoResp = covertPayPageInfoResp(project, projectStage, termDbData);
        return payPageInfoResp;
    }

    private PayPageInfoResp covertPayPageInfoResp(Project project, ProjectStage projectStage, TermCacheData termDbData) {
        PayPageInfoResp payPageInfoResp = new PayPageInfoResp();
//        payPageInfoResp.setProjectNo(project.getProjectNo());
        payPageInfoResp.setProjectName(project.getProjectName());
        payPageInfoResp.setMchntCd(project.getMchntCd());
        payPageInfoResp.setStageNo(projectStage.getStageNo());
        payPageInfoResp.setStageName(projectStage.getStageName());
        payPageInfoResp.setFyTermId(termDbData.getTmFuiouId());
//        阶段应收金额
        BigDecimal stageAmt = projectStage.getStageAmt();
//        阶段已收款金额，单位分
        BigDecimal stageActualAmt = projectStage.getStageActualAmt() == null ? BigDecimal.ZERO : projectStage.getStageActualAmt();

        payPageInfoResp.setStageActualAmt(stageActualAmt.toPlainString());
        payPageInfoResp.setAmt(stageAmt.subtract(stageActualAmt).toPlainString());
        return payPageInfoResp;
    }

    private TermCacheData queryTermInfo(String mchntCd) {
        MchntCfg detailByMchntCd = mchntCfgService.getDetailByMchntCd(mchntCd);
        if (detailByMchntCd == null) {
            log.info("商户未开通装修通业务");
            throw new FUException(ResponseCodeEnum.MCHNT_ZXT_BUSI_INFO_ERROR);
        }
        if (StringUtil.isBlank(detailByMchntCd.getDefaultTermId())) {
            log.info("商户装修通未绑定默认终端");
            throw new FUException(ResponseCodeEnum.MCHNT_ZXT_BUSI_INFO_ERROR);
        }
        TermCacheData termCacheData = termInfoService.queryTermInfo(detailByMchntCd.getDefaultTermId());
        if (termCacheData == null) {
            log.info("商户装修通默认终端不存在");
            throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
        }
        if (!StringUtil.trimToEmpty(termCacheData.getTmUserCd()).equals(StringUtil.trimToEmpty(mchntCd))) {
            log.info("终端所属商户与当前项目所属商户不一致");
            throw new FUException(ResponseCodeEnum.TERM_OWNER_NOT_CURR_MCHNT);
        }

        return termCacheData;
    }

    private ProjectStage queryProjectStage(String projectNo, String mchntCd, String stageNo) {
        ProjectStage projectStage = projectStageService.queryProjectStage(projectNo, mchntCd, stageNo);
        ProjectStageService.validProjectStage(projectStage);
        return projectStage;
    }


    private Project queryProject(String projectNo, String mchntCd) throws Exception {
        Project projectEntry = projectService.queryProjectByProjectNoAndMchntCd(projectNo, mchntCd);
        ProjectService.validProject(projectEntry);
        return projectEntry;
    }


    public CreateOrderResp createOrder(CreateOrderReq createOrderReq) throws Exception {
        Project project = queryProject(createOrderReq.getProjectNo(), createOrderReq.getMchntCd());
        ProjectStage projectStage = queryProjectStage(createOrderReq.getProjectNo(), createOrderReq.getMchntCd(), createOrderReq.getStageNo());
        BigDecimal reqStageActualAmt = new BigDecimal(createOrderReq.getStageActualAmt()==null?  "0" : createOrderReq.getStageActualAmt().toString());
        if (projectStage.getStageActualAmt()!=null && reqStageActualAmt.compareTo(projectStage.getStageActualAmt()) != 0) {
            log.info("项目阶段已收款发生变化，可能存在重复交易，刷新后重试");
            throw new FUException(ResponseCodeEnum.PROJECT_STATE_STATUS_NOT_ALLOWED);
        }

        TermCacheData termCacheData = termInfoService.getTermId(project.getMchntCd(),project.getStoreId());
        if (termCacheData == null) {
            log.info("终端信息不存在");
            throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
        }
        if (!StringUtil.trimToEmpty(termCacheData.getTmUserCd()).equals(StringUtil.trimToEmpty(createOrderReq.getMchntCd()))) {
            log.info("终端所属商户与当前项目所属商户不一致");
            throw new FUException(ResponseCodeEnum.TERM_OWNER_NOT_CURR_MCHNT);
        }
        if (!StringUtil.trimToEmpty(termCacheData.getStoreId()).equals(StringUtil.trimToEmpty(project.getStoreId()))) {
            log.info("终端所属门店与当前项目所属门店不一致");
            throw new FUException(ResponseCodeEnum.TERM_OWNER_NOT_CURR_MCHNT);
        }
        InsMchntCacheData insMchntCacheData = mchntService.queryMchntInfo(termCacheData.getTmUserCd());
        PreCreateDataRes preCreateDataRes = doRequestCreateOrder(createOrderReq, project, projectStage, termCacheData,insMchntCacheData);
        return covertCreateOrderResp(createOrderReq,preCreateDataRes);

    }

    private CreateOrderResp covertCreateOrderResp(CreateOrderReq createOrderReq, PreCreateDataRes preCreateDataRes) throws Exception {
        CreateOrderResp result = new CreateOrderResp();

        result.setTradeNO(preCreateDataRes.getReserved_transaction_id());
        result.setAppId(preCreateDataRes.getSdk_appid());
        result.setTimeStamp(preCreateDataRes.getSdk_timestamp());
        result.setNonceStr(preCreateDataRes.getSdk_noncestr());
        result.setPackageStr(preCreateDataRes.getSdk_package());
        result.setSignType(preCreateDataRes.getSdk_signtype());
        result.setPaySign(preCreateDataRes.getSdk_paysign());
        result.setOrderNo(DesUtils.encrypt(preCreateDataRes.getOrderNo().trim(),Constant.ORDER_NO_KEY));
        result.setPayJson(this.getPayJson(preCreateDataRes.getReserved_pay_info(), preCreateDataRes.getQr_code()));
        result.setQrCode(preCreateDataRes.getQr_code());
        //银联小程序TN码
        result.setPayInfo(preCreateDataRes.getReserved_pay_info());
        return result;

    }
    private String getPayJson(String reserved_pay_info, String qr_code) {
        if (StringUtils.isNotBlank(qr_code) && qr_code.toLowerCase().startsWith("http")) {
            return qr_code;
        }
        return reserved_pay_info;
    }

    private PreCreateDataRes doRequestCreateOrder(CreateOrderReq createOrderReq, Project project, ProjectStage projectStage, TermCacheData termCacheData, InsMchntCacheData insMchntCacheData) throws Exception {
        PreCreateDataReq  preCreate =generatorPreCreateDataReq(createOrderReq, project, projectStage, termCacheData,insMchntCacheData);
        return  sendPostCreateOrder(preCreate, createOrderReq, project, projectStage, termCacheData,insMchntCacheData);
    }

    private String covertGoodDesc(CreateOrderReq createOrderReq, InsMchntCacheData insMchntCacheData) {
        String dsc = StringUtil.isEmpty(createOrderReq.getDsc()) ? StringUtil.trimToEmpty(insMchntCacheData.getInsNmJcCn()) : createOrderReq.getDsc();
        return dsc;
    }

    public static String covertOrderNo(String fyTermId, OrderNumPreEnum orderNumPreEnum, String reqIp) {

        String orderNoPrefix = String.format("%s%s", orderNumPreEnum.getOrderNoPrefix(), fyTermId);
        //订单号
        String orderNo = String.format("%s%s", orderNoPrefix, StringUtil.random(10));

        return LRCUtil.calculateLRCCheck(orderNo, orderNoPrefix.length(), orderNoPrefix.length() + 2, reqIp);
    }

    private String getNoticeUrl() {
        return Constant.NOTICEURL;
    }

    private PreCreateDataReq generatorPreCreateDataReq(CreateOrderReq createOrderReq, Project project, ProjectStage projectStage, TermCacheData termCacheData, InsMchntCacheData insMchntCacheData) throws FUException {
        PreCreateDataReq preCreate = new PreCreateDataReq();
        try {
            LoginResp loginToken = LoginConstat.getLoginToken();
            preCreate.setIns_cd(Constant.INS_CD);
            preCreate.setMchnt_cd(termCacheData.getTmUserCd());
            preCreate.setTerm_id("88888888");
            preCreate.setTerm_ip(loginToken.getUserIp());
            preCreate.setRandom_str(StringUtil.random(10));
            preCreate.setGoods_des(covertGoodDesc(createOrderReq,insMchntCacheData));
            preCreate.setGoods_detail(String.format("%s-%s", project.getProjectName(), projectStage.getStageName()));
            preCreate.setMchnt_order_no(covertOrderNo(termCacheData.getTmFuiouId(), OrderNumPreEnum.ZXT, loginToken.getUserIp()));
            preCreate.setOrder_amt(createOrderReq.getAmt().toString());
            preCreate.setTxn_begin_ts(DateFormatUtils.format("yyyyMMddHHmmss"));
            preCreate.setNotify_url(getNoticeUrl());
            preCreate.setTrade_type(OrderCommonUtil.getOrderType(createOrderReq.getPayType()));
            preCreate.setOpenid("");
            //增加商圈主子商户判断
            if (StringUtil.isEmpty(createOrderReq.getSubUserId())) {
                preCreate.setSub_openid(createOrderReq.getUserId());
                preCreate.setSub_appid(ScanPayTypeEnum.JSAPI.getPayType().equals(createOrderReq.getPayType()) ? createOrderReq.getAppId() : "");
            } else {
                preCreate.setSub_openid(createOrderReq.getSubUserId());
                preCreate.setSub_appid(ScanPayTypeEnum.JSAPI.getPayType().equals(createOrderReq.getPayType()) ? createOrderReq.getSubAppId() : "");
            }

            preCreate.setCurr_type("CNY");

            preCreate.setReserved_fy_term_id(termCacheData.getTmFuiouId());

            //如果前端传递了mpAppid和mpOpenId 则 wpos 请求接口里面 公众号的放openid里面，小程序的放sub_openid里面
            if (StringUtil.isNotEmpty(createOrderReq.getMpOpenid()) && StringUtil.isNotEmpty(createOrderReq.getMpAppid())) {
                LogWriter.info(this, String.format("前端下单请求参数中包含了小程序的appid=%s 和open id=%s", createOrderReq.getMpAppid(), createOrderReq.getMpOpenid()));
                preCreate.setOpenid(createOrderReq.getUserId());
                preCreate.setSub_openid(createOrderReq.getMpOpenid());
                preCreate.setSub_appid(createOrderReq.getMpAppid());
            }

            //银行合作项目底层改造新增字段
            preCreate.setReserved_ori_busi_id(OriBusiIdEnum.ZXT.getCode());
            preCreate.encodeSign(Constant.INS_CD_PRIVATE_KEY);
            return preCreate;
        } catch (Exception e) {
            LogWriter.error(this, "OrderService preCreateOrder对参数进行加密时出现异常:", e);
            throw new FUException(ResponseCodeEnum.ORDER_ENCODSIGN_ERROR);
        }
    }


    /**
     * 请求下单接口
     *
     * @param preCreate
     * @param insMchntCacheData
     */
    private PreCreateDataRes sendPostCreateOrder(PreCreateDataReq preCreate, CreateOrderReq createOrderReq, Project project, ProjectStage projectStage, TermCacheData termCacheData, InsMchntCacheData insMchntCacheData) throws Exception {

        Map<String, String> param = new HashMap<String, String>(1);
        try {
            param.put("req", URLEncoder.encode(XMapUtil.toXML(preCreate, "GBK"), "GBK"));
        } catch (Exception e) {
            LogWriter.error("OrderService sendPostCreateOrder对req进行转换xml字符串出现异常:", e);
            throw new FUException(ResponseCodeEnum.ORDER_CXML_ERROR);
        }
        FuiouHttpPoster http = new FuiouHttpPoster();
        http.setCharset("gbk");
        http.setUrl(Constant.WPOS_HOST_WXPRECREATE);

        LogWriter.info(String.format("OrderService sendPostCreateOrder下单接口请求,url=%s,req=%s", Constant.WPOS_HOST_WXPRECREATE, JSONObject.toJSONString(param)));
        String res = http.postUTF8(param);
        res = URLDecoder.decode(res, "GBK");
        LogWriter.info("OrderService sendPostCreateOrder下单接口返回:" + res);
        PreCreateDataRes resultRes = XMapUtil.parseStr2Obj(PreCreateDataRes.class, res);
        resultRes.setOrderNo(preCreate.getMchnt_order_no());
        if (!Constant.WPOS_SUCCESS_CODE.equals(resultRes.getResult_code())) {
            LogWriter.info(this, String.format("OrderService sendPostCreateOrder请求下单接口返回错误code=%s;msg=%s;appid=%s;mchntCd=%s;orderNo=%s",
                    resultRes.getResult_code(), resultRes.getResult_msg(), preCreate.getSub_appid(), preCreate.getMchnt_cd(), preCreate.getMchnt_order_no()));
            throw new FUException(ResponseCodeEnum.RUNTIME_EXCEPTION.getCode(), String.format("%s-%s", resultRes.getResult_code(), resultRes.getResult_msg()));
        }
        this.insertPreMchtOrder(resultRes, preCreate, createOrderReq, project, projectStage, termCacheData,insMchntCacheData);
        return resultRes;
    }

    /***
     * <AUTHOR>
     * @Date 2021/12/6 14:40
     * @Description 插入商户订单表
     * @return void
     **/
    private TxnLog insertPreMchtOrder(PreCreateDataRes resultRes, PreCreateDataReq preCreate, CreateOrderReq createOrderReq, Project project, ProjectStage projectStage, TermCacheData termCacheData, InsMchntCacheData insMchntCacheData) throws Exception {
        LogWriter.info(this, "更新商户订单表 start");
        try {
            TxnLog orderInf = generatorTxnLog(resultRes, preCreate, createOrderReq, project, projectStage, termCacheData);

            int insert = txnLogService.insert(orderInf);
            if (insert < 0) {
                throw new FUException(ResponseCodeEnum.ORDER_CREATE_ERROR);
            }
            tpayOrderService.insert(tpayOrderService.covertTerminalsOrderInfo(orderInf,termCacheData,insMchntCacheData));
            LogWriter.info("更新商户订单表 end");

            return orderInf;
        } catch (Exception e) {
            LogWriter.error(this, "更新商户订单表出现异常", e);
            throw e;
        }
    }

    private TxnLog generatorTxnLog(PreCreateDataRes resultRes, PreCreateDataReq preCreate, CreateOrderReq createOrderReq, Project project, ProjectStage projectStage, TermCacheData termCacheData) {
        TxnLog result = new TxnLog();
        Date now = new Date();
        result.setOrderNo(preCreate.getMchnt_order_no());
        result.setTradeDt(DateFormatUtils.format(now, "yyyyMMdd"));
        result.setProjectNo(project.getProjectNo());
        result.setMchntCd(project.getMchntCd());
        result.setStageNo(projectStage.getStageNo());
        result.setStoreId(project.getStoreId());
        result.setOrderType(preCreate.getTrade_type());
        result.setOrderAmt(new BigDecimal(preCreate.getOrder_amt()));
        result.setPayState(OrderStatusEnum.INIT_STATUS.getCode());
        result.setFyTermId(termCacheData.getTmFuiouId());
        result.setFyFettleDt(resultRes.getReserved_fy_settle_dt());
        result.setFyTraceNo(resultRes.getReserved_fy_trace_no());
        result.setChannelOrderId(resultRes.getReserved_channel_order_id());
        result.setTransactionId(resultRes.getReserved_transaction_id());
        result.setGoodsDes(preCreate.getGoods_des());
        result.setOpenId(resultRes.getSub_openid());
        result.setCreateTime(now);
        return result;
    }



    public boolean orderResultNotic(String req) throws Exception {
        OrderResultNoticData resultData = covertOrderResultNoticeData(req);
        if (resultData == null) return false;

        TxnLog txnLogOrder=txnLogService.queryOrderInfo(resultData.getMchnt_cd(),resultData.getMchnt_order_no());
        if(txnLogOrder==null)
        {
            LogWriter.info(this,String.format("回调参数对应的订单信息为空，直接返回"));
            return false;
        }
        return updateOrderStatus(resultData,txnLogOrder);
    }


    /**
     * @Description:  wpos回调参数验证
     * @param resultData :
     * @return:
     * @Author: 程军
     * @Date: 2023/12/8 14:38
     */
    private   boolean validNoticeParam(OrderResultNoticData resultData) {

        if(resultData==null)
        {
            LogWriter.info("wpos回调内容转换对象为空");
            return false;
        }
        if(StringUtils.isBlank(resultData.getMchnt_order_no()))
        {
            LogWriter.info("wpos回调内容商户订单号为空");
            return false;
        }
        if(StringUtils.isBlank(resultData.getMchnt_cd()))
        {
            LogWriter.info("wpos回调内容商户号为空");
            return false;
        }
        if (validNoticeSign(resultData)) return false;

        return true;
    }
    private  boolean validNoticeSign(OrderResultNoticData resultData) {
        if(!resultData.validSign())
        {
            LogWriter.info("wpos回调验签失败");
            return true;
        }
        return false;
    }
    private  OrderResultNoticData covertOrderResultNoticeData(String req) throws UnsupportedEncodingException {
        String res = URLDecoder.decode(req, "GBK");
        LogWriter.info("--------------OrderService orderResultNotice----------start接口返回:" + res);
        OrderResultNoticData resultData = XMapUtil.parseStr2Obj(OrderResultNoticData.class, res);
        LogWriter.info("--------------OrderService orderResultNotice----------解析返回数据:" + JSONObject.toJSONString(resultData));
        //参数校验
        if (!validNoticeParam(resultData))
        {
            return null;
        }
        return resultData;
    }


    /**
     * 通知回调接口
     * @param resultData
     * @return
     * @throws Exception
     */
    public boolean updateOrderStatus( OrderResultNoticData resultData, TxnLog txnLogOrder) throws Exception{
        LogWriter.info("--------------OrderService orderResultNotice----------解析返回数据:"+resultData.getMchnt_order_no());
        //查询订单是否存在
        if(txnLogOrder == null || !OrderStatusEnum.INIT_STATUS.getCode().equals(txnLogOrder.getPayState())){
            LogWriter.info(this,"订单不存在或者订单状态非00");
            return false;
        }
        if(Constant.WPOS_SUCCESS_CODE.equals(resultData.getResult_code())){
            //支付成功
            txnLogOrder.setPayState(OrderStatusEnum.PAY_SUCCESS.getCode());//成功
            txnLogOrder.setPayTime(DateUtils.parseDate(resultData.getTxn_fin_ts(), "yyyyMMddHHmmss"));//支付完成时间
            txnLogOrder.setTransactionId(resultData.getTransaction_id());
            //如果回调过来的channel_order_id不为空才需要更新
            if(StringUtil.isNotEmpty(resultData.getReserved_channel_order_id()) )
            {
                txnLogOrder.setChannelOrderId(resultData.getReserved_channel_order_id());
            }
            txnLogOrder.setFyFettleDt(resultData.getReserved_fy_settle_dt());
            // 设置实收金额和优惠金额
            setRealAmtCouponFee(txnLogOrder,resultData);

            // 2020-02-10 17:23:47  程军   台卡回调计算代金券优惠金额
            ParseCashCouponUtil.calculateWechatFee(txnLogOrder,resultData.getOrder_amt(),resultData.getReserved_settlement_amt());

        } else{
            //失败
            txnLogOrder.setPayState(OrderStatusEnum.PAY_FAIL.getCode());
        }
        LogWriter.info("---------------------------come here");
        //乐观更新
        txnLogOrder.setOldPayStatus(OrderStatusEnum.INIT_STATUS.getCode());
        txnLogOrder.setUpdateTime(new Date());
        txnLogOrder.setRespMsg(resultData.getResult_code()+":"+resultData.getResult_msg());


        int updateResult = txnLogService.update(txnLogOrder);
        if(updateResult>0)
        {
            tpayOrderService.update(txnLogOrder,resultData);
            if(OrderStatusEnum.PAY_SUCCESS.getCode().equals(txnLogOrder.getPayState()))
            {
                OrderCallBackReq orderCallBackReq=new OrderCallBackReq();
                orderCallBackReq.setPay_state(txnLogOrder.getPayState());
                callBackPayService.updateProjectStage(txnLogOrder,orderCallBackReq);
                msgService.addOrderMsg(txnLogOrder.getOrderNo());
                saveMchntTermInf(txnLogOrder.getMchntCd(),txnLogOrder.getStoreId(),txnLogOrder.getFyTermId(),txnLogOrder.getOrderNo());

            }
        }

        return updateResult>0;
    }



    private   void setRealAmtCouponFee(TxnLog txnLogOrder, OrderResultNoticData resultData){
        // 口碑的和支付宝的取reserved_fund_bill_list
        String fundBillList = resultData.getReserved_fund_bill_list();
        if(StringUtils.isNotBlank(fundBillList)){
            try {
                JSONObject resJson = JSONObject.parseObject(fundBillList);
                String merchant_fund = (String) resJson.get("merchant_fund");// 优惠金额
                    txnLogOrder.setCouponAmt(new BigDecimal(merchant_fund));// 其他的记录这个字段
            } catch (Exception e) {
                LogWriter.info(txnLogOrder.getOrderNo()+
                        "无法解析reserved_fund_bill_list，无法保存实收金额receipt_amount");
            }
        }
    }

    private void saveMchntTermInf(String mchntCd,String storeId,String fyTermId,String orderNo) {

        FUApiAssert.isNotBlank(ResponseCodeEnum.MCHNT_CD_EMPTY_ERROR,fyTermId);
        FUApiAssert.isNotBlank(ResponseCodeEnum.PARAM_ERROR,fyTermId);
        FUApiAssert.isNotBlank(ResponseCodeEnum.PARAM_ERROR,orderNo);

        MchntTermInf mchntTermInf = new MchntTermInf();
        mchntTermInf.setMchntCd(mchntCd);
        mchntTermInf.setTermId(fyTermId);
        mchntTermInf.setOrderNo(orderNo);
        mchntTermInf.setStoreId(storeId);
        termInfoService.insertOrUpdateMchntTermInf(mchntTermInf);
    }
}
