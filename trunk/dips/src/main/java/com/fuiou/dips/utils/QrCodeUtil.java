package com.fuiou.dips.utils;

import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import javax.swing.filechooser.FileSystemView;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 转二维码工具
 *
 * <AUTHOR>
 * @create 2023-05-29 11:50
 **/
public class QrCodeUtil {

    //CODE_WIDTH：二维码宽度，单位像素
    private static final int CODE_WIDTH = 400;
    //CODE_HEIGHT：二维码高度，单位像素
    private static final int CODE_HEIGHT = 400;
    //FRONT_COLOR：二维码前景色，0x000000 表示黑色
    private static final int FRONT_COLOR = 0x000000;
    //BACKGROUND_COLOR：二维码背景色，0xFFFFFF 表示白色
    //演示用 16 进制表示，和前端页面 CSS 的取色是一样的，注意前后景颜色应该对比明显，如常见的黑白
    private static final int BACKGROUND_COLOR = 0xFFFFFF;

    public static String createCodeToFile(String content, File codeImgFileSaveDir, String fileName) {
        try {
            LogWriter.info("QrCodeUtil.createCodeToFile 创建二维码 content：" + content +";fileName:"+fileName);
            if (StringUtils.isBlank(content) || StringUtils.isBlank(fileName)) {
                LogWriter.info("图片内容或文件名称为空");
                throw new FUException(ResponseCodeEnum.QRCODE_PIC_CREATE_ERROR);
            }
            content = content.trim();
            if (codeImgFileSaveDir == null || codeImgFileSaveDir.isFile()) {
                //二维码图片存在目录为空，默认放在桌面...
                codeImgFileSaveDir = FileSystemView.getFileSystemView().getHomeDirectory();
            }
            if (!codeImgFileSaveDir.exists()) {
                //二维码图片存在目录不存在，开始创建...
                codeImgFileSaveDir.mkdirs();
            }

            //核心代码-生成二维码
            BufferedImage bufferedImage = getBufferedImage(content);
            File codeImgFile = new File(codeImgFileSaveDir, fileName);
            ImageIO.write(bufferedImage, "png", codeImgFile);

            System.out.println("二维码图片生成成功path：" + codeImgFile.getPath());
            return codeImgFile.getName();
        } catch (Exception e) {
            LogWriter.error("QrCodeUtil.createCodeToFile 生成二维码图片异常：",e);
            throw new FUException(ResponseCodeEnum.QRCODE_PIC_CREATE_ERROR);
        }
    }

    /**
     * 生成二维码并输出到输出流, 通常用于输出到网页上进行显示，输出到网页与输出到磁盘上的文件中，区别在于最后一句 ImageIO.write
     * write(RenderedImage im,String formatName,File output)：写到文件中
     * write(RenderedImage im,String formatName,OutputStream output)：输出到输出流中
     *
     * @param content      ：二维码内容
     * @param  ：输出流，比如 HttpServletResponse 的 getOutputStream
     */
    public static String createCodeToOutputStream(String content) {
        try {
            if (StringUtils.isBlank(content)) {
                return "";
            }
            content = content.trim();
            //核心代码-生成二维码
            BufferedImage bufferedImage = getBufferedImage(content);
            ByteArrayOutputStream stream  = new ByteArrayOutputStream();
            //区别就是这一句，输出到输出流中，如果第三个参数是 File，则输出到文件中
            ImageIO.write(bufferedImage, "png", stream);
            byte[] bytes = Base64.encodeBase64(stream.toByteArray());
            String base64 = new String(bytes);
            String reStr=  "data:image/jpeg;base64,"+base64;
            System.out.println("生成二维码图片转base64成功..:"+reStr.substring(0,50));
            return reStr;
        } catch (Exception e) {
            LogWriter.error("异常",e);
        }
        return "";
    }

    //核心代码-生成二维码
    private static BufferedImage getBufferedImage(String content) throws WriterException {

        //com.google.zxing.EncodeHintType：编码提示类型,枚举类型
        Map<EncodeHintType, Object> hints = new HashMap();

        //EncodeHintType.CHARACTER_SET：设置字符编码类型
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");

        //EncodeHintType.ERROR_CORRECTION：设置误差校正
        //ErrorCorrectionLevel：误差校正等级，L = ~7% correction、M = ~15% correction、Q = ~25% correction、H = ~30% correction
        //不设置时，默认为 L 等级，等级不一样，生成的图案不同，但扫描的结果是一样的
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);

        //EncodeHintType.MARGIN：设置二维码边距，单位像素，值越小，二维码距离四周越近
        hints.put(EncodeHintType.MARGIN, 1);

        MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
        BitMatrix bitMatrix = multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, CODE_WIDTH, CODE_HEIGHT, hints);
        BufferedImage bufferedImage = new BufferedImage(CODE_WIDTH, CODE_HEIGHT, BufferedImage.TYPE_INT_BGR);
        for (int x = 0; x < CODE_WIDTH; x++) {
            for (int y = 0; y < CODE_HEIGHT; y++) {
                bufferedImage.setRGB(x, y, bitMatrix.get(x, y) ? FRONT_COLOR : BACKGROUND_COLOR);
            }
        }
        return bufferedImage;
    }

    public static void main(String[] args) {
        String url ="https://dips-test.fuioupay.com/order/toPay/0002230F0561752/7af6dc220a1c4bf19b0cc4489ff5621d";
        System.out.println(createCodeToOutputStream(url));

        String year = DateFormatUtils.format(new Date(), "yyyy");
        String month = DateFormatUtils.format(new Date(), "MM");
        System.out.println(createCodeToFile(url,  new File("/NFS_HOME/FILE_ROOT/FZG_FILE/dips/"+year+"/"+month), "qrcode2.png"));
    }

}


