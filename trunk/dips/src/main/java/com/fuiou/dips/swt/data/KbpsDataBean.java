package com.fuiou.dips.swt.data;

import com.fuiou.dips.swt.utils.BcdUtil;
import com.fuiou.dips.utils.LogWriter;
import org.apache.commons.lang.StringUtils;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;


/**
 * 注        释：rut对接 data
 * 修改时间：2017年3月10日 下午4:08:23
 * 创建时间：2017年3月10日 下午4:08:23
 *
 * <AUTHOR> E-mail:<EMAIL>
 */
public class KbpsDataBean {
    public String busiCd = "";
    public String txnDir = "";
    public String busiChnl = "";
    public String txnAmt = "";
    public String srcInsCd = "";
    public String srcStlDt = "";
    public String srcInsSsn = "";
    public String locDt = "";
    public String locTm = "";
    public String regionCd = "";
    public String srcTermId = "";
    public String phoneNo = "";
    public String trackData = "";
    public String txnCurrCd = "";
    public String cardPin = "";
    public String cardPinFmt = "";
    public String srcMchntCd = "";
    public String orderNo = "";
    public String billYymm = "";
    public String custmrPin = "";
    public String custmrPinFmt = "";
    public String custmrNoTp = "";
    public String custmrNo = "";
    public String goodsNum = "";
    public String payMd = "";
    public String addnTxnInf = "";
    public String debitAcntCd = "";
    public String creditAcntCd = "";
    public String origSsn = "";
    public String origBusiCd = "";
    public String origLocDt = "";
    public String origLocTm = "";
    public String kbpsTraceNo = "";
    public String kbpsSettleDt = "";
    public String txnFinTm = "";
    public String rspCd = "";
    public String authRspCd = "";
    public String rsnCd = "";
    public String rcvInsCd = "";
    public String rspData = "";
    public String mac = "";
    private String printData = "";
    private String icSerial = "";
    private String icReserved = "";
    private String icCardData = "";

    public KbpsDataBean() {
        this.init();
    }

    public static KbpsDataBean getInstance() {
        KbpsDataBean data = new KbpsDataBean();
        return data;
    }

    public byte[] toByte() {
        int[] lengths = {4, 1, 2, 12, 2 + 11, 8, 6, 8, 6, 4, 16, 16, 3 + 200,
                3, 8, 2, 15, 40, 4, 40, 2, 1, 20, 40, 1, 60, 2 + 28, 2 + 28, 6,
                4, 8, 6, 12, 8, 10, 4, 6, 4, 2 + 11, 3 + 200, 8};
        String space = " ";
        byte[] packet = new byte[887 + 4];
        String head = "887";
        System.arraycopy(head.getBytes(), 0, packet, 0, head.getBytes().length);
        System.arraycopy(busiCd.getBytes(), 0, packet, getSum(lengths, 0) + 4, busiCd.getBytes().length);
        System.arraycopy(txnDir.getBytes(), 0, packet, getSum(lengths, 1) + 4, txnDir.getBytes().length);
        System.arraycopy(busiChnl.getBytes(), 0, packet, getSum(lengths, 2) + 4, busiChnl.getBytes().length);
        System.arraycopy(txnAmt.getBytes(), 0, packet, getSum(lengths, 3) + 4, txnAmt.getBytes().length);
        System.arraycopy(srcInsCd.getBytes(), 0, packet, getSum(lengths, 4) + 4, srcInsCd.getBytes().length);
        System.arraycopy(srcStlDt.getBytes(), 0, packet, getSum(lengths, 5) + 4, srcStlDt.getBytes().length);
        System.arraycopy(srcInsSsn.getBytes(), 0, packet, getSum(lengths, 6) + 4, srcInsSsn.getBytes().length);
        System.arraycopy(locDt.getBytes(), 0, packet, getSum(lengths, 7) + 4, locDt.getBytes().length);
        System.arraycopy(locTm.getBytes(), 0, packet, getSum(lengths, 8) + 4, locTm.getBytes().length);
        System.arraycopy(regionCd.getBytes(), 0, packet, getSum(lengths, 9) + 4, regionCd.getBytes().length);
        System.arraycopy(srcTermId.getBytes(), 0, packet, getSum(lengths, 10) + 4, srcTermId.getBytes().length);
        System.arraycopy(phoneNo.getBytes(), 0, packet, getSum(lengths, 11) + 4, phoneNo.getBytes().length);
        String trackData = StringUtils.repeat(space, lengths[12]);
        System.arraycopy(trackData.getBytes(), 0, packet, getSum(lengths, 12) + 4, trackData.getBytes().length);
        System.arraycopy(txnCurrCd.getBytes(), 0, packet, getSum(lengths, 13) + 4, txnCurrCd.getBytes().length);
        System.arraycopy(cardPin.getBytes(), 0, packet, getSum(lengths, 14) + 4, cardPin.getBytes().length);
        System.arraycopy(cardPinFmt.getBytes(), 0, packet, getSum(lengths, 15) + 4, cardPinFmt.getBytes().length);
        System.arraycopy(srcMchntCd.getBytes(), 0, packet, getSum(lengths, 16) + 4, srcMchntCd.getBytes().length);
        System.arraycopy(orderNo.getBytes(), 0, packet, getSum(lengths, 17) + 4, orderNo.getBytes().length);
        System.arraycopy(billYymm.getBytes(), 0, packet, getSum(lengths, 18) + 4, billYymm.getBytes().length);
        System.arraycopy(custmrPin.getBytes(), 0, packet, getSum(lengths, 19) + 4, custmrPin.getBytes().length);
        System.arraycopy(custmrPinFmt.getBytes(), 0, packet, getSum(lengths, 20) + 4, custmrPinFmt.getBytes().length);
        System.arraycopy(custmrNoTp.getBytes(), 0, packet, getSum(lengths, 21) + 4, custmrNoTp.getBytes().length);
        System.arraycopy(custmrNo.getBytes(), 0, packet, getSum(lengths, 22) + 4, custmrNo.getBytes().length);
        System.arraycopy(goodsNum.getBytes(), 0, packet, getSum(lengths, 23) + 4, goodsNum.getBytes().length);
        System.arraycopy(payMd.getBytes(), 0, packet, getSum(lengths, 24) + 4, payMd.getBytes().length);
        System.arraycopy(addnTxnInf.getBytes(), 0, packet, getSum(lengths, 25) + 4, addnTxnInf.getBytes().length);
        System.arraycopy(debitAcntCd.getBytes(), 0, packet, getSum(lengths, 26) + 4, debitAcntCd.getBytes().length);
        System.arraycopy(creditAcntCd.getBytes(), 0, packet, getSum(lengths, 27) + 4, creditAcntCd.getBytes().length);
        System.arraycopy(origSsn.getBytes(), 0, packet, getSum(lengths, 28) + 4, origSsn.getBytes().length);
        System.arraycopy(origBusiCd.getBytes(), 0, packet, getSum(lengths, 29) + 4, origBusiCd.getBytes().length);
        System.arraycopy(origLocDt.getBytes(), 0, packet, getSum(lengths, 30) + 4, origLocDt.getBytes().length);
        System.arraycopy(origLocTm.getBytes(), 0, packet, getSum(lengths, 31) + 4, origLocTm.getBytes().length);
        System.arraycopy(kbpsTraceNo.getBytes(), 0, packet, getSum(lengths, 32) + 4, kbpsTraceNo.getBytes().length);
        System.arraycopy(kbpsSettleDt.getBytes(), 0, packet, getSum(lengths, 33) + 4, kbpsSettleDt.getBytes().length);
        System.arraycopy(txnFinTm.getBytes(), 0, packet, getSum(lengths, 34) + 4, txnFinTm.getBytes().length);
        System.arraycopy(rspCd.getBytes(), 0, packet, getSum(lengths, 35) + 4, rspCd.getBytes().length);
        System.arraycopy(authRspCd.getBytes(), 0, packet, getSum(lengths, 36) + 4, authRspCd.getBytes().length);
        System.arraycopy(rsnCd.getBytes(), 0, packet, getSum(lengths, 37) + 4, rsnCd.getBytes().length);
        System.arraycopy(rcvInsCd.getBytes(), 0, packet, getSum(lengths, 38) + 4, rcvInsCd.getBytes().length);
        System.arraycopy(rspData.getBytes(), 0, packet, getSum(lengths, 39) + 4, rspData.getBytes().length);

        System.arraycopy(BcdUtil.str2Bcd(mac), 0, packet, getSum(lengths, 40) + 4, BcdUtil.str2Bcd(mac).length);
        LogWriter.info(this,String.format("实际发送报文为：%s",new String(packet)));
        return packet;
    }

    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append(getBody());
        sb.append(mac);
        String body = sb.toString();
        String head = StringUtils.leftPad("" + body.length(), 4, "0");

        LogWriter.info(String.format("发送KBPS报文===[%s]", new String(head + body)));
        return head + body;
    }

    public String getBody() {
        StringBuffer sb = new StringBuffer();
        sb.append(busiCd).append(txnDir).append(busiChnl).append(txnAmt)
                .append(srcInsCd).append(srcStlDt).append(srcInsSsn).append(
                locDt).append(locTm).append(regionCd).append(srcTermId)
                .append(phoneNo).append(trackData).append(txnCurrCd).append(
                cardPin).append(cardPinFmt).append(srcMchntCd).append(
                orderNo).append(billYymm).append(custmrPin).append(
                custmrPinFmt).append(custmrNoTp).append(custmrNo)
                .append(goodsNum).append(payMd).append(addnTxnInf).append(
                debitAcntCd).append(creditAcntCd).append(origSsn)
                .append(origBusiCd).append(origLocDt).append(origLocTm).append(
                kbpsTraceNo).append(kbpsSettleDt).append(txnFinTm)
                .append(rspCd).append(authRspCd).append(rsnCd).append(rcvInsCd)
                .append(rspData);
        return sb.toString();
    }

    /**
     * 解析kbps返回结果
     *
     * @param results 后台返回的字节数据
     * @return KbpsDataBean
     */
    public static KbpsDataBean getInstanceByBytes(byte[] results) {

        Map<Integer, byte[]> resultMap = splitBytes(results);

        KbpsDataBean kbpsData = new KbpsDataBean();

        kbpsData.busiCd = new String(resultMap.get(0));
        kbpsData.txnDir = new String(resultMap.get(1));
        kbpsData.busiChnl = new String(resultMap.get(2));
        kbpsData.txnAmt = new String(resultMap.get(3));
        kbpsData.srcInsCd = new String(resultMap.get(4));
        kbpsData.srcStlDt = new String(resultMap.get(5));
        kbpsData.srcInsSsn = new String(resultMap.get(6));
        kbpsData.locDt = new String(resultMap.get(7));
        kbpsData.locTm = new String(resultMap.get(8));
        kbpsData.regionCd = new String(resultMap.get(9));
        kbpsData.srcTermId = new String(resultMap.get(10));
        kbpsData.phoneNo = new String(resultMap.get(11));
        kbpsData.trackData = new String(resultMap.get(12));
        kbpsData.txnCurrCd = new String(resultMap.get(13));
        kbpsData.cardPin = new String(resultMap.get(14));
        kbpsData.cardPinFmt = new String(resultMap.get(15));
        kbpsData.srcMchntCd = new String(resultMap.get(16));
        kbpsData.orderNo = new String(resultMap.get(17));
        kbpsData.billYymm = new String(resultMap.get(18));
        kbpsData.custmrPin = new String(resultMap.get(19));
        kbpsData.custmrPinFmt = new String(resultMap.get(20));
        kbpsData.custmrNoTp = new String(resultMap.get(21));
        kbpsData.custmrNo = new String(resultMap.get(22));
        kbpsData.goodsNum = new String(resultMap.get(23));
        kbpsData.payMd = new String(resultMap.get(24));
        kbpsData.addnTxnInf = new String(resultMap.get(25));
        kbpsData.debitAcntCd = new String(resultMap.get(26));
        kbpsData.creditAcntCd = new String(resultMap.get(27));
        kbpsData.origSsn = new String(resultMap.get(28));
        kbpsData.origBusiCd = new String(resultMap.get(29));
        kbpsData.origLocDt = new String(resultMap.get(30));
        kbpsData.origLocTm = new String(resultMap.get(31));
        kbpsData.kbpsTraceNo = new String(resultMap.get(32));
        kbpsData.kbpsSettleDt = new String(resultMap.get(33));
        kbpsData.txnFinTm = new String(resultMap.get(34));
        kbpsData.rspCd = new String(resultMap.get(35));
        kbpsData.authRspCd = new String(resultMap.get(36));
        kbpsData.rsnCd = new String(resultMap.get(37));
        kbpsData.rcvInsCd = new String(resultMap.get(38));
        try {
            kbpsData.rspData = new String(resultMap.get(39), "GBK");
        } catch (UnsupportedEncodingException e) {
            LogWriter.error("异常",e);
        }
        kbpsData.icSerial = new String(resultMap.get(40));
        kbpsData.icReserved = new String(resultMap.get(41));
        return kbpsData;
    }

    public static Map<Integer, byte[]> splitBytes(byte[] results) {
        Map<Integer, byte[]> map = new HashMap<Integer, byte[]>();
        int[] lengths = {4, 1, 2, 12, 2 + 11, 8, 6, 8, 6, 4, 16, 16, 3 + 200,
                3, 8, 2, 15, 40, 4, 40, 2, 1, 20, 40, 1, 60, 2 + 28, 2 + 28, 6,
                4, 8, 6, 12, 8, 10, 4, 6, 4, 2 + 11, 3 + 200, 3}; // , 8, 3+256, 8
        //开始拆分bytes
        int byteLength = lengths.length;
        int start = 0;
        for (; start < byteLength; start++) {
            byte[] data = new byte[lengths[start]];
            int beginIndex = getSum(lengths, start);
            LogWriter.info( String.format("result size:%d,beginIndex:%d,data:%d,start:%d", results.length, beginIndex, data.length, start));
            System.arraycopy(results, beginIndex, data, 0, lengths[start]);
            if (start == 42 && StringUtils.isNotEmpty(new String(data))) {//icCardData
                byte[] icLen = new byte[3];
                System.arraycopy(results, beginIndex, icLen, 0, 3);
                LogWriter.info("icDataLen : " + BcdUtil.hexStrToAsc(BcdUtil.bytesToHexString(icLen)));
                // 默认 200长度 磁道数据
                int dataLen = 200;
                if (StringUtils.isNotBlank(BcdUtil.hexStrToAsc(BcdUtil.bytesToHexString(icLen)).trim())) {
                    dataLen = Integer.parseInt(cutLeftZeroOfMoney(BcdUtil.hexStrToAsc(BcdUtil.bytesToHexString(icLen))));
                }
                data = new byte[dataLen];
                System.arraycopy(results, beginIndex + 3, data, 0, dataLen);
            }
            map.put(start, data);
        }

        //获取打印报文数据.1157-对应kbps返回报文字节长度
        if (results.length > 1157) {
            int printLen = results.length - 1157;
            byte[] printData = new byte[printLen];
            System.arraycopy(results, 1157, printData, 0, printLen);
            map.put(start, printData);
        } else {
            map.put(start, "".getBytes());
        }
        return map;
    }

    public static KbpsDataBean getPQ27Instance(String src) throws Exception {
        String head = src.substring(0, 4);
        String body = src.substring(4);
        if (body.length() != Integer.parseInt(head)) {
        }
        body = body.replace((char) 0, ' ');
        int[] lengths = {4, 1, 2, 12, 2 + 11, 8, 6, 8, 6, 4, 16, 16, 3 + 200, 3, 8, 2, 15, 40, 4, 40, 2, 1, 20, 40, 1, 60, 2 + 28, 2 + 28, 6, 4, 8,
                6, 12, 8, 10, 4, 6, 4, 2 + 11, 3 + 200,};
        int[] lengthsBmPos = {4, 1, 2, 12, 2 + 11, 8, 6, 8, 6, 4, 16, 16, 3 + 200, 3, 8, 2, 15, 40, 4, 40, 2, 1, 20, 40, 1, 60, 2 + 28, 2 + 28, 6,
                4, 8, 6, 12, 8, 10, 4, 6, 4, 2 + 11, 3 + 200, 8, 1000};

        String[] values = null;
        if (body.length() > 887)// 便民POS
            values = split(body, lengthsBmPos);
        else
            values = split(body, lengths);

        KbpsDataBean kbpsData = new KbpsDataBean();

        kbpsData.busiCd = values[0];
        kbpsData.txnDir = values[1];
        kbpsData.busiChnl = values[2];
        kbpsData.txnAmt = values[3];
        kbpsData.srcInsCd = values[4];
        kbpsData.srcStlDt = values[5];
        kbpsData.srcInsSsn = values[6];
        kbpsData.locDt = values[7];
        kbpsData.locTm = values[8];
        kbpsData.regionCd = values[9];
        kbpsData.srcTermId = values[10];
        kbpsData.phoneNo = values[11];
        kbpsData.trackData = values[12];
        kbpsData.txnCurrCd = values[13];
        kbpsData.cardPin = values[14];
        kbpsData.cardPinFmt = values[15];
        kbpsData.srcMchntCd = values[16];
        kbpsData.orderNo = values[17];
        kbpsData.billYymm = values[18];
        kbpsData.custmrPin = values[19];
        kbpsData.custmrPinFmt = values[20];
        kbpsData.custmrNoTp = values[21];
        kbpsData.custmrNo = values[22];
        kbpsData.payMd = values[24];
        kbpsData.addnTxnInf = values[25];
        kbpsData.debitAcntCd = values[26];
        kbpsData.creditAcntCd = values[27];
        kbpsData.origSsn = values[28];
        kbpsData.origBusiCd = values[29];
        kbpsData.origLocDt = values[30];
        kbpsData.origLocTm = values[31];
        kbpsData.kbpsTraceNo = values[32];
        kbpsData.kbpsSettleDt = values[33];
        kbpsData.txnFinTm = values[34];
        kbpsData.rspCd = values[35];
        kbpsData.authRspCd = values[36];
        kbpsData.rsnCd = values[37];
        kbpsData.rcvInsCd = values[38];
        kbpsData.rspData = values[39];
        if (body.length() > 887) {// 便民POS
            kbpsData.mac = values[40];
            kbpsData.printData = values[41];
        }
        return kbpsData;
    }

    private static String[] split(String src, int[] lengths) {
        String[] results = new String[lengths.length];
        for (int i = 0; i < lengths.length; i++) {
            int beginIndex = getSum(lengths, i);
            //支持中文
            results[i] = substring(src, beginIndex, beginIndex + lengths[i]);
        }
        return results;
    }

    private static int getSum(int[] lengths, int index) {
        int sum = 0;
        if (index == 0)
            return sum;
        else {
            for (int i = 0; i < index; i++) {
                sum = sum + lengths[i];
            }
        }
        return sum;
    }

    private void init() {
        String space = " ";
        this.busiCd = StringUtils.repeat(space, 4);
        this.txnDir = StringUtils.repeat(space, 1);
        this.busiChnl = StringUtils.repeat(space, 2);
        this.txnAmt = StringUtils.repeat(space, 12);
        this.srcInsCd = StringUtils.repeat(space, 2 + 11);
        this.srcStlDt = StringUtils.repeat(space, 8);
        this.srcInsSsn = StringUtils.repeat(space, 6);
        this.locDt = StringUtils.repeat(space, 8);
        this.locTm = StringUtils.repeat(space, 6);
        this.regionCd = StringUtils.repeat(space, 4);
        this.srcTermId = StringUtils.repeat(space, 16);
        this.phoneNo = StringUtils.repeat(space, 16);
        this.trackData = StringUtils.repeat(space, 3 + 200);
        this.txnCurrCd = StringUtils.repeat(space, 3);
        this.cardPin = StringUtils.repeat(space, 8);
        this.cardPinFmt = StringUtils.repeat(space, 2);
        this.srcMchntCd = StringUtils.repeat(space, 15);
        this.orderNo = StringUtils.repeat(space, 40);
        this.billYymm = StringUtils.repeat(space, 4);
        this.custmrPin = StringUtils.repeat(space, 40);
        this.custmrPinFmt = StringUtils.repeat(space, 2);
        this.custmrNoTp = StringUtils.repeat(space, 1);
        this.custmrNo = StringUtils.repeat(space, 20);
        this.goodsNum = StringUtils.repeat(space, 40);
        this.payMd = StringUtils.repeat(space, 1);
        this.addnTxnInf = StringUtils.repeat(space, 60);
        this.debitAcntCd = StringUtils.repeat(space, 2 + 28);
        this.creditAcntCd = StringUtils.repeat(space, 2 + 28);
        this.origSsn = StringUtils.repeat(space, 6);
        this.origBusiCd = StringUtils.repeat(space, 4);
        this.origLocDt = StringUtils.repeat(space, 8);
        this.origLocTm = StringUtils.repeat(space, 6);
        this.kbpsTraceNo = StringUtils.repeat(space, 12);
        this.kbpsSettleDt = StringUtils.repeat(space, 8);
        this.txnFinTm = StringUtils.repeat(space, 10);
        this.rspCd = StringUtils.repeat(space, 4);
        this.authRspCd = StringUtils.repeat(space, 6);
        this.rsnCd = StringUtils.repeat(space, 4);
        this.rcvInsCd = StringUtils.repeat(space, 2 + 11);
        this.rspData = StringUtils.repeat(space, 3 + 200);
        this.mac = StringUtils.repeat(space, 8);
        this.icSerial = StringUtils.repeat(space, 3);
        this.icReserved = StringUtils.repeat(space, 8);
        this.icCardData = StringUtils.repeat(space, 3 + 256);
        this.printData = StringUtils.repeat(space, 1000);
    }



    public String getRspDataMsg() {
        if (StringUtils.isBlank(rspData) || !rspData.contains("resultMsg")) {
            return "";
        }
        int startIdx = rspData.indexOf("resultMsg") + "resultMsg".length() + 1;
        int endIdx = rspData.lastIndexOf("resultMsg") - 2;
        return rspData.substring(startIdx, endIdx);
    }

    public String getIcSerial() {
        return icSerial;
    }

    public void setIcSerial(String icSerial) {
        this.icSerial = trim(icSerial);
    }

    public String getIcReserved() {
        return icReserved;
    }

    public void setIcReserved(String icReserved) {
        this.icReserved = trim(icReserved);
    }

    public String getIcCardData() {
        return icCardData;
    }

    public void setIcCardData(String icCardData) {
        this.icCardData = trim(icCardData);
    }

    public String getBusiCd() {
        return busiCd.trim();
    }

    public void setBusiCd(String busiCd) {
        if (this.busiCd.length() == busiCd.length())
            this.busiCd = busiCd;
        else
            this.busiCd = busiCd
                    + this.busiCd.substring(busiCd.length());
    }

    public String getTxnDir() {
        return txnDir.trim();
    }

    public void setTxnDir(String txnDir) {
        if (this.txnDir.length() == txnDir.length())
            this.txnDir = txnDir;
        else
            this.txnDir = txnDir
                    + this.txnDir.substring(txnDir.length());
    }

    public String getBusiChnl() {
        return busiChnl.trim();
    }

    public void setBusiChnl(String busiChnl) {
        if (this.busiChnl.length() == busiChnl.length())
            this.busiChnl = busiChnl;
        else
            this.busiChnl = busiChnl
                    + this.busiChnl.substring(busiChnl.length());
    }

    public String getTxnAmt() {
        return txnAmt.trim();
    }

    public void setTxnAmt(String txnAmt) {
        if (!StringUtils.isEmpty(txnAmt)) {
            this.txnAmt = StringUtils.repeat("0", 12);
            if (this.txnAmt.length() == txnAmt.length())
                this.txnAmt = txnAmt;
            else
                this.txnAmt = this.txnAmt.substring(txnAmt.length()) + txnAmt;
        }

    }

    public String getSrcInsCd() {
        return srcInsCd.trim();
    }

    public void setSrcInsCd(String srcInsCd) {
        if (this.srcInsCd.length() == srcInsCd.length())
            this.srcInsCd = srcInsCd;
        else
            this.srcInsCd = srcInsCd
                    + this.srcInsCd.substring(srcInsCd.length());
    }

    public String getSrcStlDt() {
        return srcStlDt.trim();
    }

    public void setSrcStlDt(String srcStlDt) {
        if (this.srcStlDt.length() == srcStlDt.length())
            this.srcStlDt = srcStlDt;
        else
            this.srcStlDt = srcStlDt
                    + this.srcStlDt.substring(srcStlDt.length());
    }

    public String getSrcInsSsn() {
        return srcInsSsn.trim();
    }

    public void setSrcInsSsn(String srcInsSsn) {
        if (this.srcInsSsn.length() == srcInsSsn.length())
            this.srcInsSsn = srcInsSsn;
        else
            this.srcInsSsn = srcInsSsn
                    + this.srcInsSsn.substring(srcInsSsn.length());
    }

    public String getLocDt() {
        return locDt.trim();
    }

    public void setLocDt(String locDt) {
        if (this.locDt.length() == locDt.length())
            this.locDt = locDt;
        else
            this.locDt = locDt
                    + this.locDt.substring(locDt.length());
    }

    public String getLocTm() {
        return locTm.trim();
    }

    public void setLocTm(String locTm) {
        if (this.locTm.length() == locTm.length())
            this.locTm = locTm;
        else
            this.locTm = locTm
                    + this.locTm.substring(locTm.length());
    }

    public String getRegionCd() {
        return regionCd.trim();
    }

    public void setRegionCd(String regionCd) {
        if (this.regionCd.length() == regionCd.length())
            this.regionCd = regionCd;
        else
            this.regionCd = regionCd
                    + this.regionCd.substring(regionCd.length());
    }

    public String getSrcTermId() {
        return srcTermId.trim();
    }

    public void setSrcTermId(String srcTermId) {
        if (this.srcTermId.length() == srcTermId.length())
            this.srcTermId = srcTermId;
        else
            this.srcTermId = srcTermId
                    + this.srcTermId.substring(srcTermId.length());
    }

    public String getPhoneNo() {
        return phoneNo.trim();
    }

    public void setPhoneNo(String phoneNo) {
        if (this.phoneNo.length() == phoneNo.length())
            this.phoneNo = phoneNo;
        else
            this.phoneNo = phoneNo
                    + this.phoneNo.substring(phoneNo.length());
    }

    public String getTrackData() {
        return trackData;
    }

    //2磁道，3磁道信息
    public void setTrackData(String trackData) {
        this.trackData = trackData;
    }

    public String getTxnCurrCd() {
        return txnCurrCd.trim();
    }

    public void setTxnCurrCd(String txnCurrCd) {
        if (this.txnCurrCd.length() == txnCurrCd.length())
            this.txnCurrCd = txnCurrCd;
        else
            this.txnCurrCd = txnCurrCd
                    + this.txnCurrCd.substring(txnCurrCd.length());
    }

    public String getCardPin() {
        return cardPin;
    }

    public void setCardPin(String cardPin) {
        if (this.cardPin.length() == cardPin.length())
            this.cardPin = cardPin;
        else
            this.cardPin = cardPin
                    + this.cardPin.substring(cardPin.length());
    }

    public String getCardPinFmt() {
        return cardPinFmt.trim();
    }

    public void setCardPinFmt(String cardPinFmt) {
        if (this.cardPinFmt.length() == cardPinFmt.length())
            this.cardPinFmt = cardPinFmt;
        else
            this.cardPinFmt = cardPinFmt
                    + this.cardPinFmt.substring(cardPinFmt.length());
    }

    public String getSrcMchntCd() {
        return srcMchntCd.trim();
    }

    public void setSrcMchntCd(String srcMchntCd) {
        if (this.srcMchntCd.length() == srcMchntCd.length())
            this.srcMchntCd = srcMchntCd;
        else
            this.srcMchntCd = srcMchntCd
                    + this.srcMchntCd.substring(srcMchntCd.length());
    }

    public String getOrderNo() {
        return orderNo.trim();
    }

    public void setOrderNo(String orderNo) {
        if (this.orderNo.length() == orderNo.length())
            this.orderNo = orderNo;
        else
            this.orderNo = orderNo
                    + this.orderNo.substring(orderNo.length());
    }

    public String getBillYymm() {
        return billYymm.trim();
    }

    public void setBillYymm(String billYymm) {
        if (this.billYymm.length() == billYymm.length())
            this.billYymm = billYymm;
        else
            this.billYymm = billYymm
                    + this.billYymm.substring(billYymm.length());

    }

    public String getCustmrPin() {
        return custmrPin.trim();
    }

    public void setCustmrPin(String custmrPin) {
        if (this.custmrPin.length() == custmrPin.length())
            this.custmrPin = custmrPin;
        else
            this.custmrPin = custmrPin
                    + this.custmrPin.substring(custmrPin.length());
    }

    public String getCustmrPinFmt() {
        return custmrPinFmt.trim();
    }

    public void setCustmrPinFmt(String custmrPinFmt) {
        if (this.custmrPinFmt.length() == custmrPinFmt.length())
            this.custmrPinFmt = custmrPinFmt;
        else
            this.custmrPinFmt = custmrPinFmt
                    + this.custmrPinFmt.substring(custmrPinFmt.length());

    }

    public String getCustmrNoTp() {
        return custmrNoTp.trim();
    }

    public void setCustmrNoTp(String custmrNoTp) {
        if (this.custmrNoTp.length() == custmrNoTp.length())
            this.custmrNoTp = custmrNoTp;
        else
            this.custmrNoTp = custmrNoTp
                    + this.custmrNoTp.substring(custmrNoTp.length());
    }

    public String getCustmrNo() {
        return custmrNo.trim();
    }

    public void setCustmrNo(String custmrNo) {
        if (this.custmrNo.length() == custmrNo.length())
            this.custmrNo = custmrNo;
        else
            this.custmrNo = custmrNo
                    + this.custmrNo.substring(custmrNo.length());
    }

    public String getGoodsNum() {
        return goodsNum.trim();
    }

    public void setGoodsNum(String goodsNum) {
        if (this.goodsNum.length() == goodsNum.length())
            this.goodsNum = goodsNum;
        else
            this.goodsNum = goodsNum
                    + this.goodsNum.substring(goodsNum.length());
    }

    public String getPayMd() {
        return payMd.trim();
    }

    public void setPayMd(String payMd) {
        if (this.payMd.length() == payMd.length())
            this.payMd = payMd;
        else
            this.payMd = payMd
                    + this.payMd.substring(payMd.length());
    }

    public String getAddnTxnInf() {
        return addnTxnInf.trim();
    }

    public void setAddnTxnInf(String addnTxnInf) {
        if (this.addnTxnInf.length() == addnTxnInf.length())
            this.addnTxnInf = addnTxnInf;
        else
            this.addnTxnInf = addnTxnInf
                    + this.addnTxnInf.substring(addnTxnInf.length());
    }

    public String getDebitAcntCd() {
        return debitAcntCd.trim();
    }

    public void setDebitAcntCd(String debitAcntCd) {
        if (this.debitAcntCd.length() == debitAcntCd.length())
            this.debitAcntCd = debitAcntCd;
        else
            this.debitAcntCd = debitAcntCd
                    + this.debitAcntCd.substring(debitAcntCd.length());
    }

    public String getCreditAcntCd() {
        return creditAcntCd.trim();
    }

    public void setCreditAcntCd(String creditAcntCd) {
        if (this.creditAcntCd.length() == creditAcntCd.length())
            this.creditAcntCd = creditAcntCd;
        else
            this.creditAcntCd = creditAcntCd
                    + this.creditAcntCd.substring(creditAcntCd.length());
    }

    public String getOrigSsn() {
        return origSsn.trim();
    }

    public void setOrigSsn(String origSsn) {
        if (this.origSsn.length() == origSsn.length())
            this.origSsn = origSsn;
        else
            this.origSsn = origSsn
                    + this.origSsn.substring(origSsn.length());
    }

    public String getOrigBusiCd() {
        return origBusiCd.trim();
    }

    public void setOrigBusiCd(String origBusiCd) {
        if (this.origBusiCd.length() == origBusiCd.length())
            this.origBusiCd = origBusiCd;
        else
            this.origBusiCd = origBusiCd
                    + this.origBusiCd.substring(origBusiCd.length());
    }

    public String getOrigLocDt() {
        return origLocDt.trim();
    }

    public void setOrigLocDt(String origLocDt) {
        if (this.origLocDt.length() == origLocDt.length())
            this.origLocDt = origLocDt;
        else
            this.origLocDt = origLocDt
                    + this.origLocDt.substring(origLocDt.length());

    }

    public String getOrigLocTm() {
        return origLocTm.trim();
    }

    public void setOrigLocTm(String origLocTm) {
        if (this.origLocTm.length() == origLocTm.length())
            this.origLocTm = origLocTm;
        else
            this.origLocTm = origLocTm
                    + this.origLocTm.substring(origLocTm.length());
    }

    public String getKbpsTraceNo() {
        return kbpsTraceNo.trim();
    }

    public void setKbpsTraceNo(String kbpsTraceNo) {
        if (this.kbpsTraceNo.length() == kbpsTraceNo.length())
            this.kbpsTraceNo = kbpsTraceNo;
        else
            this.kbpsTraceNo = kbpsTraceNo
                    + this.kbpsTraceNo.substring(kbpsTraceNo.length());
    }

    public String getKbpsSettleDt() {
        return kbpsSettleDt.trim();
    }

    public void setKbpsSettleDt(String kbpsSettleDt) {
        this.kbpsSettleDt = kbpsSettleDt;
    }

    public String getTxnFinTm() {
        return txnFinTm.trim();
    }

    public void setTxnFinTm(String txnFinTm) {
        this.txnFinTm = txnFinTm;
    }

    public String getRspCd() {
        return rspCd.trim();
    }

    public void setRspCd(String rspCd) {
        this.rspCd = rspCd;
    }

    public String getAuthRspCd() {
        return authRspCd.trim();
    }

    public void setAuthRspCd(String authRspCd) {
        this.authRspCd = authRspCd;
    }

    public String getRsnCd() {
        return rsnCd.trim();
    }

    public void setRsnCd(String rsnCd) {
        this.rsnCd = rsnCd;
    }

    public String getRcvInsCd() {
        return rcvInsCd.trim();
    }

    public void setRcvInsCd(String rcvInsCd) {
        this.rcvInsCd = rcvInsCd;
    }

    public String getRspData() {
        return rspData.trim();
    }

    public void setRspData(String rspData) {
        if (this.rspData.length() == rspData.getBytes().length)
            this.rspData = rspData;
        else
            this.rspData = rspData
                    + this.rspData.substring(rspData.getBytes().length);
    }

    public String getMac() {
        return mac.trim();
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getPrintData() {
        return StringUtils.isEmpty(printData) ? "" : printData.trim();
    }

    public void setPrintData(String printData) {
        this.printData = printData;
    }

    private static String substring(String str, int begIndex, int endIndex) {
        if (str == null) {
            return "";
        } else if (begIndex >= endIndex) {
            return "";
        } else {
            byte b[] = null;
            b = str.getBytes();
            return (new String(b, begIndex, endIndex - begIndex));
        }
    }

    // 去掉金额右边的0
    public static String cutLeftZeroOfMoney(String str) {
        int flag = 0;
        int len = str.length();
        for (int i = 0; i < len; i++) {
            if (str.charAt(i) != '0') {
                flag = i;
                break;
            }
        }
        if (flag == 0 && str.charAt(0) == '0') {
            return "0";
        }
        return str.substring(flag);
    }

    // 是否请求成功
    public boolean isSuccess() {
        if ("0000".equals(this.getRspCd())) {
            return true;
        }
        return false;
    }

    // 获取账户可提现金额
    public String getWithdrawAmt() {
        if (isSuccess() && StringUtils.isNotEmpty(this.getRspData())
                && this.getRspData().length() > 28) {
            return this.getRspData().substring(16, 28);
        }
        return "";
    }

    // 获取手续费
    public String getFeeAmt() {
        if (this.isSuccess() && StringUtils.isNotBlank(rspData) && this.rspData.length() > 12) {
            return rspData.substring(0, 12);
        }
        return "";
    }

    // 获取手续费率
    public String getFeeRate() {
        if (this.isSuccess() && StringUtils.isNotBlank(rspData) && this.rspData.length() > 12) {
            return rspData.substring(12);
        }
        return "";
    }

    /**
     * 方法注释： trim 去除 '
     * 修改内容：
     * 修改时间： 2017年2月20日 上午11:44:17
     *
     * @param str
     * @return
     * <AUTHOR>
     */
    public static String trim(String str) {
        if (null != str) {
            return str.trim().replace("'", "");
        }
        return "";
    }

    public static void main(String[] args) {
        KbpsDataBean dataBean = new KbpsDataBean();
        dataBean.setBusiCd("TX09");
        dataBean.setTxnAmt("000000000000");
        dataBean.setSrcInsCd("111111");
        dataBean.setSrcStlDt("20171219");
        // System.out.println(Arrays.toString(dataBean.toByte()));
        System.out.println(dataBean.getBody());
        System.out.println(dataBean.toString());

    }

}
