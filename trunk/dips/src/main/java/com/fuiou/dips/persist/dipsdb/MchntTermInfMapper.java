package com.fuiou.dips.persist.dipsdb;

import com.fuiou.dips.persist.beans.MchntTermInf;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MchntTermInfMapper {
    // 插入记录
    int insert(MchntTermInf mchntTermInf);

    // 根据ID查询记录
    MchntTermInf selectByMchntAndStoreId(@Param("mchntCd") String mchntCd, @Param("storeId") String storeId);

    // 更新记录
    int updateByRowId(MchntTermInf mchntTermInf);

    // 删除记录
    int deleteById(Long id);

    // 查询所有记录
    List<MchntTermInf> selectAll();
}