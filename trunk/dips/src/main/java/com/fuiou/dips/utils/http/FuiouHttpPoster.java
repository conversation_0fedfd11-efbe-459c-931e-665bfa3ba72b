/**
 * 
 */
package com.fuiou.dips.utils.http;


import com.fuiou.dips.utils.LogWriter;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.InputStreamRequestEntity;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.httpclient.protocol.Protocol;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * <AUTHOR>
 * 
 */
public class FuiouHttpPoster {

	public FuiouHttpPoster() {
	}

	private String url;

	private String charset;

	public void setUrl(String url) {
		this.url = url;
	}

	public void setCharset(String charset) {
		this.charset = charset;
	}

	public FuiouHttpPoster(String url, String charset) {
		super();
		this.url = url;
		this.charset = charset;
	}

	public FuiouHttpPoster(String url) {
		super();
		this.url = url;
		this.charset = Charset.defaultCharset().name();
	}

	/**
	 * 发送参数包
	 * 
	 * @param parameters
	 * @return
	 * @throws IOException
	 */
	public String post(final Map<String, String> parameters) throws Exception {
		return post(new PostMethodCallback() {

			@Override
			public void doInPostMethod(PostMethod postMethod) {
				NameValuePair[] nameValuePairs = new NameValuePair[parameters
						.size()];
				Set<Entry<String, String>> set = parameters.entrySet();
				int i = 0;
				// 设置查询参数
				for (Entry<String, String> entry : set) {
					NameValuePair pair = new NameValuePair(entry.getKey(),
							entry.getValue());
					nameValuePairs[i] = pair;
					i++;
				}
				// 发送参数包
				postMethod.setRequestBody(nameValuePairs);

			}
		});
	}
	
	/**
	 * 发送参数包
	 * 
	 * @param parameters
	 * @return
	 * @throws IOException
	 */
	public String postUTF8(final Map<String, String> parameters) throws Exception {
		return postUTF8(new PostMethodCallback() {

			@Override
			public void doInPostMethod(PostMethod postMethod) {
				NameValuePair[] nameValuePairs = new NameValuePair[parameters
						.size()];
				Set<Entry<String, String>> set = parameters.entrySet();
				int i = 0;
				// 设置查询参数
				for (Entry<String, String> entry : set) {
					NameValuePair pair = new NameValuePair(entry.getKey(),
							entry.getValue());
					nameValuePairs[i] = pair;
					i++;
				}
				// 发送参数包
				postMethod.setRequestBody(nameValuePairs);

			}
		});
	}

	/**
	 * 使用http协议发送xmltext到url
	 * 
	 * @param url
	 *            将要发送的地址
	 * @param xmltext
	 *            将要发送的内容
	 * @return http返回码
	 */
	public String post(final String body) throws Exception {
		return post(new PostMethodCallback() {

			@Override
			public void doInPostMethod(PostMethod postMethod) {

				try {
					InputStream instream = new ByteArrayInputStream(
							body.getBytes(charset));
					postMethod.setRequestEntity(new InputStreamRequestEntity(
							instream, body.getBytes(charset).length));
				} catch (UnsupportedEncodingException e) {
					LogWriter.error("异常",e);
				}
			}
		});
	}
	
	public String newPost(final String body) throws Exception {
		return post(new PostMethodCallback() {

			@Override
			public void doInPostMethod(PostMethod postMethod) {

				try {
					postMethod.setRequestEntity(new StringRequestEntity(body,"application/json","UTF-8"));
				} catch (UnsupportedEncodingException e) {
					LogWriter.error("异常",e);
				}
			}
		});
	}
	
	/**
	 * 使用http协议发送xmltext到url
	 * 
	 * @param url
	 *            将要发送的地址
	 * @param xmltext
	 *            将要发送的内容
	 * @return http返回码
	 * @throws LoanException
	 * @throws Exception
	 */
	private String postUTF8(PostMethodCallback callback) throws Exception {
		PostMethod xmlpost = null;
		HttpClient httpclient = new HttpClient();
		try {
			// https设置
			if (url.indexOf("https://") != -1) {
				// 创建SSL连接
				@SuppressWarnings("deprecation")
				Protocol myhttps = new Protocol("https",
						new MySSLSocketFactory(), 443);
				Protocol.registerProtocol("https", myhttps);
			}

			httpclient.getHttpConnectionManager().getParams()
					.setConnectionTimeout(1000 * 60);
			httpclient.getHttpConnectionManager().getParams().setSoTimeout(60 * 1000);
			xmlpost = new PostMethod(url);
			httpclient.getParams().setParameter(
					HttpMethodParams.HTTP_CONTENT_CHARSET, "UTF-8");
			httpclient.getParams().setContentCharset("UTF-8");
			// xmlpost.setRequestHeader("content-type", "text/xml; charset=" +
			// charset);

			// 内部回调，发送数据，设置参数用
			callback.doInPostMethod(xmlpost);
			// 执行请求
			httpclient.executeMethod(xmlpost);

			// 获取返回信息
			InputStream ips = xmlpost.getResponseBodyAsStream();
			List<Byte> byteList = new ArrayList<Byte>();

			int is = 0;
			while ((is = ips.read()) != -1)
				byteList.add((byte) is);

			byte[] allb = new byte[byteList.size()];
			for (int j = 0; j < byteList.size(); j++)
				allb[j] = byteList.get(j);
			String responseString = new String(allb, "UTF-8");
			// LogWriter.debug(this, "HTTP返回码=" + responseStatCode);
			LogWriter.info(this, "应答数据=" + responseString);

			if (url.indexOf("https://") != -1)
				Protocol.unregisterProtocol("https");
			return responseString;
		} catch (IOException ex2) {
			ex2.printStackTrace();
			LogWriter.debug(this, "报文发送到[" + url + "]失败:" + ex2.getMessage());
			throw new Exception("报文发送异常");
		} catch (Exception ex) {
			ex.printStackTrace();
			LogWriter.debug(this, "报文发送到[" + url + "]失败:" + ex.getMessage());
			throw new Exception("报文发送异常");
		} finally {
			try {
				if(xmlpost != null)
					xmlpost.releaseConnection();
				if(httpclient != null)
					httpclient.getHttpConnectionManager().closeIdleConnections(0);
			} catch (Exception e) {
				LogWriter.error("异常",e);
			}
		}
	}

	/**
	 * 使用http协议发送xmltext到url
	 * 
	 * @param url
	 *            将要发送的地址
	 * @param xmltext
	 *            将要发送的内容
	 * @return http返回码
	 * @throws LoanException
	 * @throws Exception
	 */
	private String post(PostMethodCallback callback) throws Exception {
		PostMethod xmlpost = null;
		HttpClient httpclient = new HttpClient();
		try {
			// https设置
			if (url.indexOf("https://") != -1) {
				// 创建SSL连接
				@SuppressWarnings("deprecation")
				Protocol myhttps = new Protocol("https",
						new MySSLSocketFactory(), 443);
				Protocol.registerProtocol("https", myhttps);
			}	
			httpclient.getHttpConnectionManager().getParams().setConnectionTimeout(60 * 1000);
			httpclient.getHttpConnectionManager().getParams().setSoTimeout(60 * 1000);
			xmlpost = new PostMethod(url);
			httpclient.getParams().setParameter(
					HttpMethodParams.HTTP_CONTENT_CHARSET, charset);
			httpclient.getParams().setContentCharset(charset);
			// xmlpost.setRequestHeader("content-type", "text/xml; charset=" +
			// charset);

			// 内部回调，发送数据，设置参数用
			callback.doInPostMethod(xmlpost);
			// 执行请求
			httpclient.executeMethod(xmlpost);

			// 获取返回信息
			InputStream ips = xmlpost.getResponseBodyAsStream();
			List<Byte> byteList = new ArrayList<Byte>();

			int is = 0;
			while ((is = ips.read()) != -1)
				byteList.add((byte) is);

			byte[] allb = new byte[byteList.size()];
			for (int j = 0; j < byteList.size(); j++)
				allb[j] = byteList.get(j);
			String responseString = new String(allb, charset);
			// LogWriter.debug(this, "HTTP返回码=" + responseStatCode);
			LogWriter.info(this, "应答数据=" + responseString);

			if (url.indexOf("https://") != -1)
				Protocol.unregisterProtocol("https");
			return responseString;
		} catch (IOException ex2) {
			ex2.printStackTrace();
			LogWriter.debug(this, "报文发送到[" + url + "]失败:" + ex2.getMessage());
			throw new Exception("报文发送异常");
		} catch (Exception ex) {
			ex.printStackTrace();
			LogWriter.debug(this, "报文发送到[" + url + "]失败:" + ex.getMessage());
			throw new Exception("报文发送异常");
		} finally {
			try {
				if(xmlpost != null)
					xmlpost.releaseConnection();
				if(httpclient != null)
					httpclient.getHttpConnectionManager().closeIdleConnections(0);
			} catch (Exception e) {
				LogWriter.error("异常",e);
			}
		}
	}

	/**
	 * PostMethod回调处理
	 * 
	 */
	public interface PostMethodCallback {
		public void doInPostMethod(PostMethod postMethod);
	}


	/**增加timeout 超时时间参数
	 * <AUTHOR>
	 * @date 2020-11-27
	 * @param body
	 * @param timeout
	 * @return
	 * @throws Exception
	 */
	public String postTimeOut(final String body,int timeout) throws Exception {
		return postTimeOut(new PostMethodCallback() {

			@Override
			public void doInPostMethod(PostMethod postMethod) {

				try {
					postMethod.setRequestEntity(new StringRequestEntity(body,"application/json","UTF-8"));
				} catch (UnsupportedEncodingException e) {
					LogWriter.error("异常",e);
				}
			}
		},timeout);
	}


	/**
	 * <AUTHOR>
	 * 使用http协议发送xmltext到url
	 * @return http返回码
	 * @throws Exception
	 */
	private String postTimeOut(PostMethodCallback callback,int timeout) throws Exception {
		PostMethod xmlpost = null;
		HttpClient httpclient = new HttpClient();
		try {
			// https设置
			if (url.indexOf("https://") != -1) {
				// 创建SSL连接
				@SuppressWarnings("deprecation")
				Protocol myhttps = new Protocol("https",
						new MySSLSocketFactory(), 443);
				Protocol.registerProtocol("https", myhttps);
			}
			httpclient.getHttpConnectionManager().getParams().setConnectionTimeout(timeout);
			httpclient.getHttpConnectionManager().getParams().setSoTimeout(timeout);
			xmlpost = new PostMethod(url);
			httpclient.getParams().setParameter(
					HttpMethodParams.HTTP_CONTENT_CHARSET, charset);
			httpclient.getParams().setContentCharset(charset);
			// xmlpost.setRequestHeader("content-type", "text/xml; charset=" +
			// charset);

			// 内部回调，发送数据，设置参数用
			callback.doInPostMethod(xmlpost);
			// 执行请求
			httpclient.executeMethod(xmlpost);

			// 获取返回信息
			InputStream ips = xmlpost.getResponseBodyAsStream();
			List<Byte> byteList = new ArrayList<Byte>();

			int is = 0;
			while ((is = ips.read()) != -1)
				byteList.add((byte) is);

			byte[] allb = new byte[byteList.size()];
			for (int j = 0; j < byteList.size(); j++)
				allb[j] = byteList.get(j);
			String responseString = new String(allb, charset);
			// LogWriter.debug(this, "HTTP返回码=" + responseStatCode);
			LogWriter.info(this, "应答数据=" + responseString);

			if (url.indexOf("https://") != -1)
				Protocol.unregisterProtocol("https");
			return responseString;
		} catch (IOException ex2) {
			ex2.printStackTrace();
			LogWriter.debug(this, "报文发送到[" + url + "]失败:" + ex2.getMessage());
			throw new Exception("报文发送异常");
		} catch (Exception ex) {
			ex.printStackTrace();
			LogWriter.debug(this, "报文发送到[" + url + "]失败:" + ex.getMessage());
			throw new Exception("报文发送异常");
		} finally {
			try {
				if(xmlpost != null)
					xmlpost.releaseConnection();
				if(httpclient != null)
					httpclient.getHttpConnectionManager().closeIdleConnections(0);
			} catch (Exception e) {
				LogWriter.error("异常",e);
			}
		}
	}


	/**
	 * 发送参数包
	 *
	 * @param parameters
	 * @return
	 * @throws IOException
	 */
	public String postUTF8(final Map<String, String> parameters,int timeOut) throws Exception {
		return postUTF8(new PostMethodCallback() {

			@Override
			public void doInPostMethod(PostMethod postMethod) {
				NameValuePair[] nameValuePairs = new NameValuePair[parameters
						.size()];
				Set<Entry<String, String>> set = parameters.entrySet();
				int i = 0;
				// 设置查询参数
				for (Entry<String, String> entry : set) {
					NameValuePair pair = new NameValuePair(entry.getKey(),
							entry.getValue());
					nameValuePairs[i] = pair;
					i++;
				}
				// 发送参数包
				postMethod.setRequestBody(nameValuePairs);

			}
		},timeOut);
	}
	/**
	 * 使用http协议发送xmltext到url
	 *
	 * @param url
	 *            将要发送的地址
	 * @param xmltext
	 *            将要发送的内容
	 * @return http返回码
	 * @throws LoanException
	 * @throws Exception
	 */
	private String postUTF8(PostMethodCallback callback,int timeOut) throws Exception {
		PostMethod xmlpost = null;
		HttpClient httpclient = new HttpClient();
		try {
			// https设置
			if (url.indexOf("https://") != -1) {
				// 创建SSL连接
				@SuppressWarnings("deprecation")
				Protocol myhttps = new Protocol("https",
						new MySSLSocketFactory(), 443);
				Protocol.registerProtocol("https", myhttps);
			}

			httpclient.getHttpConnectionManager().getParams()
					.setConnectionTimeout(timeOut);
			httpclient.getHttpConnectionManager().getParams().setSoTimeout(timeOut);
			xmlpost = new PostMethod(url);
			httpclient.getParams().setParameter(
					HttpMethodParams.HTTP_CONTENT_CHARSET, "UTF-8");
			httpclient.getParams().setContentCharset("UTF-8");
			// xmlpost.setRequestHeader("content-type", "text/xml; charset=" +
			// charset);

			// 内部回调，发送数据，设置参数用
			callback.doInPostMethod(xmlpost);
			// 执行请求
			httpclient.executeMethod(xmlpost);

			// 获取返回信息
			InputStream ips = xmlpost.getResponseBodyAsStream();
			List<Byte> byteList = new ArrayList<Byte>();

			int is = 0;
			while ((is = ips.read()) != -1)
				byteList.add((byte) is);

			byte[] allb = new byte[byteList.size()];
			for (int j = 0; j < byteList.size(); j++)
				allb[j] = byteList.get(j);
			String responseString = new String(allb, "UTF-8");
			// LogWriter.debug(this, "HTTP返回码=" + responseStatCode);
			LogWriter.info(this, "应答数据=" + responseString);

			if (url.indexOf("https://") != -1)
				Protocol.unregisterProtocol("https");
			return responseString;
		} catch (IOException ex2) {
			ex2.printStackTrace();
			LogWriter.debug(this, "报文发送到[" + url + "]失败:" + ex2.getMessage());
			throw new Exception("报文发送异常");
		} catch (Exception ex) {
			ex.printStackTrace();
			LogWriter.debug(this, "报文发送到[" + url + "]失败:" + ex.getMessage());
			throw new Exception("报文发送异常");
		} finally {
			try {
				if(xmlpost != null)
					xmlpost.releaseConnection();
				if(httpclient != null)
					httpclient.getHttpConnectionManager().closeIdleConnections(0);
			} catch (Exception e) {
				LogWriter.error("异常",e);
			}
		}
	}
}
