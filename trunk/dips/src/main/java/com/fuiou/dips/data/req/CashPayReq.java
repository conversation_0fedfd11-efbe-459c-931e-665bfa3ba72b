package com.fuiou.dips.data.req;

import com.fuiou.dips.valid.Money;
import com.fuiou.dips.valid.MoneyNotBlank;
import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

public class CashPayReq  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目编号
     */
    @NotBlank(message = "项目编号不能为空")
    @Size(min = 1,
            max = 32,
            message = "项目编号需在1-32字符")
    private String projectNo;



    /**
     * 商户号
     */
    @NotBlank(message = "商户号不能为空")
    @Size(min = 1,
            max = 20,
            message = "商户号需在5-20字符")
    private String mchntCd;

    /**
     * 阶段编号
     */
    @NotBlank(message = "阶段编号不能为空")
    @Size(min = 1,
            max = 32,
            message = "阶段编号需在5-32字符")
    private String stageNo;

    @Money(message = "已收款金额未获取", hasZero = true)
    @NotBlank(message = "已收款金额未获取")
    private String stageActualAmt;

    /**
     * 本次需支付金额
     */
    @MoneyNotBlank(message = "支付金额不能为空")
    private String amt;


    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getStageNo() {
        return stageNo;
    }

    public void setStageNo(String stageNo) {
        this.stageNo = stageNo;
    }

    public String getStageActualAmt() {
        return stageActualAmt;
    }

    public void setStageActualAmt(String stageActualAmt) {
        this.stageActualAmt = stageActualAmt;
    }

    public String getAmt() {
        return amt;
    }

    public void setAmt(String amt) {
        this.amt = amt;
    }
}
