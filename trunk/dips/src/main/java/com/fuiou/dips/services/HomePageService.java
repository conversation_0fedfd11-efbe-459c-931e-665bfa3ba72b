package com.fuiou.dips.services;

import cn.hutool.core.lang.Pair;
import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.data.req.CountProjectReq;
import com.fuiou.dips.data.req.MchntHomePageReq;
import com.fuiou.dips.data.req.PageProjectReq;
import com.fuiou.dips.data.resp.*;
import com.fuiou.dips.enums.OrderStatusEnum;
import com.fuiou.dips.enums.ProjectEnum;
import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.persist.beans.Project;
import com.fuiou.dips.persist.beans.ProjectStage;
import com.fuiou.dips.persist.beans.TxnLog;
import com.fuiou.dips.persist.dipsdb.ProjectMapper;
import com.fuiou.dips.persist.dipsdb.ProjectStageMapper;
import com.fuiou.dips.persist.dipsdb.TxnLogMapper;
import com.fuiou.dips.utils.CollectionUtils;
import com.fuiou.dips.utils.DateUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 首页服务
 */
@Service
public class HomePageService {
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectStageMapper projectStageMapper;
    @Resource
    private TxnLogMapper txnLogMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectStageService  projectStageService;

    /**
     * 获取商户首页数据
     * @return
     */
    public MchntHomePageResp getMchntHomePageResp(MchntHomePageReq mchntHomePageReq) {
        MchntHomePageResp mchntHomePageResp=new MchntHomePageResp();
        String mchntCd = LoginConstat.getLoginToken().getMchntInfo().getMchntCd();
        MchntHomeDataStatsResp dataStats = this.getDataStats(mchntHomePageReq.getQueryMonth(), mchntHomePageReq.getStartDate(), mchntHomePageReq.getEndDate(),
                mchntCd, mchntHomePageReq.getStoreIds());
        mchntHomePageResp.setDataStatsResp(dataStats);
        return mchntHomePageResp;
    }

    public PageRespBase<PageProjectResp> getProjectList(String mchntCd,PageProjectReq projectReq) {
        List<String> storeIdsByMchntCd = getStoreIdsByMchntCd(mchntCd);
        PageHelper.startPage(projectReq.getPage(), projectReq.getLimit());
        List<PageProjectResp> pageProjectResps = projectMapper.selectProjectsWithStageAndCustomer(mchntCd, projectReq.getCustomerName(),
                projectReq.getPhone(),projectReq.getProjectSt(), storeIdsByMchntCd);
        return new PageRespBase<>(new PageInfo<>(pageProjectResps));
    }

    public CountProjectResp countProjectWithStatus(String mchntCd, CountProjectReq countProjectReq) {
        return projectMapper.countProjectWithStatus(mchntCd,
                getStoreIdsByMchntCd(mchntCd), countProjectReq.getCustomerName(), countProjectReq.getPhone());
    }

    /**
     * 本月新增项目金额:本月新建项目总金额的和
     * 本月新增项目数:默认当前自然月
     * 本月收款金额:默认当前自然月-查订单表
     * 待收款金额:目前所有未收款金额的和,筛选后计算时间段内未收款金额和，-项目阶段付款表
     * 待收款项目数:所有正在进行中的项目数量和,筛选后计算时间段内正在进行中的项目数量和- 项目阶段付款表
     *
     * @param queryMonth
     * @param startDateStr
     * @param endDateStr
     * @param mchntCd
     * @param storeIds
     * @return
     */
    @LogAnnotation(value = "数据统计", methodName = "查询")
    public MchntHomeDataStatsResp getDataStats(String queryMonth,String startDateStr, String endDateStr, String mchntCd, List<String> storeIds) {
        MchntHomeDataStatsResp mchntHomeDataStatsResp = new MchntHomeDataStatsResp();
        storeIds = getFilteredStoreIdsByMchntCd(mchntCd, storeIds);

        if(StringUtils.isNotBlank(queryMonth)){
            mchntHomeDataStatsResp=getDataStatsByMonth(queryMonth, mchntCd, storeIds);
        }else{
            mchntHomeDataStatsResp=getDataStatsByDate(startDateStr, endDateStr, mchntCd, storeIds);
        }
        return mchntHomeDataStatsResp;
    }

    /**
     * 获取商户门店ID集合，并与传入的 storeIds 做交集处理
     * @param mchntCd 商户编号
     * @param storeIds 用户传入的门店ID列表
     * @return 经过校验和过滤后的门店ID列表
     */
    public List<String> getFilteredStoreIdsByMchntCd(String mchntCd, List<String> storeIds) {
        List<String> dbStoreIds = getStoreIdsByMchntCd(mchntCd);
        if (CollectionUtils.isEmpty(dbStoreIds)) {
            return new ArrayList<>(); // 返回空列表更安全
        }
        if (CollectionUtils.isEmpty(storeIds)) {
            return dbStoreIds;
        }

        List<String> filteredStoreIds = new ArrayList<>(storeIds);
        filteredStoreIds.retainAll(dbStoreIds);
        return filteredStoreIds;
    }
    /**
     * 获取商户门店ID集合
     * @param mchntCd
     * @return
     */
    private List<String> getStoreIdsByMchntCd(String mchntCd) {
        List<String> storeIds=new ArrayList<>();
        List<StoreInfoResp> storesByMchntCd = projectService.getStoresByMchntCd(mchntCd);
        for (StoreInfoResp storeInfoResp : storesByMchntCd) {
            storeIds.add(storeInfoResp.getStoreId());
        }
        return storeIds;
    }

    /**
     * 查询指定时间范围内的项目信息并统计总金额与项目编号列表
     *
     * @param startDate 起始时间
     * @param endDate   结束时间
     * @param mchntCd   商户编号
     * @param storeIds  门店ID集合
     * @return Pair<BigDecimal, List<String>> 第一个为总金额，第二个为项目编号列表
     */
    private Pair<BigDecimal, List<String>> calculateProjectData(Timestamp startDate, Timestamp endDate, String mchntCd, List<String> storeIds) {
        List<Project> projects = getProjects(startDate, endDate, mchntCd, storeIds);
        return calculateProjectStats(projects);
    }

    /**
     * 计算待收款金额及进行中的项目数
     *
     * @param stages      项目阶段列表
     * @return Pair<BigDecimal, Long> 第一个元素为待收款金额，第二个为待收项目数
     */
    private Pair<BigDecimal, Long> calculatePendingReceipts(List<ProjectStage> stages) {
        BigDecimal pendingAmount = BigDecimal.ZERO;
        long pendingCount = 0;

        for (ProjectStage stage : stages) {
            if (ProjectEnum.StageStEnum.ONGOING.getState().equals(stage.getStageSt())) {
                pendingCount++;
            }
            pendingAmount = pendingAmount.add(stage.getStageAmt())
                    .subtract(stage.getStageActualAmt())
                    .add(stage.getRefundAmt());
        }

        return new Pair<>(pendingAmount, pendingCount);
    }

    /**
     * 计算增长比例：(current - last) / last * 100%
     *
     * @param current 当前值
     * @param last 上月值
     * @return 百分比（保留2位小数）
     */
    private BigDecimal calculateGrowthRate(BigDecimal current, BigDecimal last) {
        if (last.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return current.subtract(last)
                .divide(last, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(100))
                .setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算项目数量变化百分比
     *
     * @param currentSize 当前项目数量
     * @param lastSize 上月项目数量
     * @return 百分比（整数）
     */
    private Long calculateProjectGrowthRate(int currentSize, int lastSize) {
        if (lastSize == 0) {
            return 0L;
        }
        return Math.round(((double) currentSize - lastSize) / lastSize * 100);
    }

    /**
     * 计算指定时间范围内的收款金额
     *
     * @param mchntCd 商户编号
     * @param start 开始时间
     * @param end 结束时间
     * @param payStates 支付状态列表
     * @return 收款金额
     */
    private BigDecimal calculateReceiptAmount(String mchntCd, Timestamp start, Timestamp end, List<String> payStates) {
        List<TxnLog> txnLogs = getTxnLogs(mchntCd, start, end, payStates);
        BigDecimal amount = BigDecimal.ZERO;

        for (TxnLog log : txnLogs) {
            if ("1".equals(log.getTradeType())) {
                amount = amount.add(log.getOrderAmt()).subtract(log.getRefundAmt());
            }
        }

        return amount;
    }

    /**
     * 根据月份维度统计数据
     *
     * 数据包括：
     * - 本月新增项目金额
     * - 本月新增项目数
     * - 较上月增长/降低金额
     * - 较上月增长/降低项目数
     * - 本月收款金额
     * - 待收款金额
     * - 待收款项目数
     */
    private MchntHomeDataStatsResp getDataStatsByMonth(String queryMonth, String mchntCd, List<String> storeIds) {
        // 获取当前月与上月的时间段
        Timestamp[] currentMonthTimestamp = DateUtils.getMonthlyTimestamp(queryMonth);
        Timestamp[] lastMonthTimestamp = DateUtils.getLastMonthTimestamp(queryMonth);

        // 查询本月项目信息
        Pair<BigDecimal, List<String>> currentStats = calculateProjectData(currentMonthTimestamp[0], currentMonthTimestamp[1], mchntCd, storeIds);
        BigDecimal currentMonthAmount = currentStats.getKey();
        List<String> currentProjectNos = currentStats.getValue();

        // 查询上月项目信息
        Pair<BigDecimal, List<String>> lastStats = calculateProjectData(lastMonthTimestamp[0], lastMonthTimestamp[1], mchntCd, storeIds);
        BigDecimal lastMonthAmount = lastStats.getKey();
        List<String> lastProjectNos = lastStats.getValue();

        // 构建响应对象并填充基础数据
        MchntHomeDataStatsResp stats = new MchntHomeDataStatsResp();
        stats.setMonthlyNewProjectsAmount(currentMonthAmount);
        stats.setMonthlyNewProjectsCount((long) currentProjectNos.size());

        // 设置金额增长率
        stats.setAmountChangeComparedToLastMonth(
                calculateGrowthRate(currentMonthAmount, lastMonthAmount)
        );

        // 设置项目数增长率
        stats.setProjectCountChangeComparedToLastMonth(
                calculateProjectGrowthRate(currentProjectNos.size(), lastProjectNos.size())
        );

        // 设置本月收款金额
        List<String> validPayStates = Arrays.asList(OrderStatusEnum.PAY_SUCCESS.getCode(), OrderStatusEnum.REFUND_SUCCESS.getCode());
        stats.setMonthlyReceiptsAmount(calculateReceiptAmount(mchntCd, currentMonthTimestamp[0], currentMonthTimestamp[1], validPayStates));

        // 设置上月收款金额并计算增长率
        BigDecimal lastReceiptAmount = calculateReceiptAmount(mchntCd, lastMonthTimestamp[0], lastMonthTimestamp[1], validPayStates);
        stats.setReceiptAmountChangeComparedToLastMonth(
                calculateGrowthRate(stats.getMonthlyReceiptsAmount(), lastReceiptAmount)
        );

        // 设置待收款信息
        List<String> validStageStates = Arrays.asList(
                ProjectEnum.StageStEnum.ONGOING.getState(),
                ProjectEnum.StageStEnum.NON_START.getState()
        );
        List<ProjectStage> projectStages = DateUtils.isCurrentMonth(queryMonth)
                ? projectStageService.queryProjectStages(mchntCd, null, validStageStates)
                : projectStageService.queryProjectStages(mchntCd, currentProjectNos, validStageStates);

        Pair<BigDecimal, Long> pendingReceipts = calculatePendingReceipts(projectStages);
        stats.setPendingReceiptsAmount(pendingReceipts.getKey());
        stats.setPendingReceiptsProjectCount(pendingReceipts.getValue());
        return stats;
    }

    /**
     * 根据开始和结束时间统计数据
     *
     * 数据包括：
     * - 新增项目金额与数量
     * - 待收款金额与项目数
     * - 本月收款金额
     */
    private MchntHomeDataStatsResp getDataStatsByDate(String startDateStr, String endDateStr, String mchntCd, List<String> storeIds) {
        // 时间转换
        Timestamp[] timestamps = DateUtils.convertToTimestamps(startDateStr, endDateStr);
        Timestamp start = timestamps[0];
        Timestamp end = timestamps[1];

        // 获取项目信息
        Pair<BigDecimal, List<String>> result = calculateProjectData(start, end, mchntCd, storeIds);
        BigDecimal totalAmount = result.getKey();
        List<String> projectNos = result.getValue();

        // 构造返回对象
        MchntHomeDataStatsResp stats = new MchntHomeDataStatsResp();
        stats.setMonthlyNewProjectsAmount(totalAmount);
        stats.setMonthlyNewProjectsCount((long) projectNos.size());

        // 设置待收款信息
        List<String> validStageStates = Arrays.asList(
                ProjectEnum.StageStEnum.ONGOING.getState(),
                ProjectEnum.StageStEnum.NON_START.getState()
        );
        List<ProjectStage> stages = projectStageService.queryProjectStages(mchntCd, projectNos, validStageStates);

        Pair<BigDecimal, Long> pendingReceipts = calculatePendingReceipts(stages);
        stats.setPendingReceiptsAmount(pendingReceipts.getKey());
        stats.setPendingReceiptsProjectCount(pendingReceipts.getValue());

        // 设置收款金额
        List<String> validPayStates = Arrays.asList(OrderStatusEnum.PAY_SUCCESS.getCode(), OrderStatusEnum.REFUND_SUCCESS.getCode());
        stats.setMonthlyReceiptsAmount(calculateReceiptAmount(mchntCd, start, end, validPayStates));

        return stats;
    }



    @LogAnnotation(value = "本月新增项目金额和数量", methodName = "查询")
    private Pair<BigDecimal, List<String>> calculateProjectStats(List<Project> projects) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<String> projectNos = new ArrayList<>();
        for (Project project : projects) {
            totalAmount = totalAmount.add(project.getProjectAmt());
            projectNos.add(project.getProjectNo());
        }
        return new Pair<>(totalAmount, projectNos);
    }

    @LogAnnotation(value = "项目信息", methodName = "查询")
    private List<Project> getProjects(Timestamp startDate, Timestamp endDate, String mchntCd, List<String> storeIds) {
        return projectMapper.selectProjectList(mchntCd, storeIds, startDate, endDate);
    }


    @LogAnnotation(value = "交易订单", methodName = "查询")
    private  List<TxnLog> getTxnLogs(String mchntCd,Timestamp startDate, Timestamp endDate,List<String> validPayStates) {
        return txnLogMapper.selectByMchntCdAndDate(mchntCd, startDate, endDate, validPayStates);
    }


}
