package com.fuiou.dips.framework.interceptor;

import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.consts.URLConstant;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.enums.EmployeeRoleTypeEnum;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.enums.UserTypeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.services.TokenService;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.NetworkUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Set;

/***
 * <AUTHOR>
 * @Description 鉴权
 * @Date 2025/4/17 10:03 
 **/
public class AuthInterceptor implements HandlerInterceptor {

    @Resource
    private TokenService tokenService;

    /** 校验开关 **/
    public static final boolean IS_CHECK = true ;


    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        if(!IS_CHECK){
            return true;
        }

        //校验token
        checkToken(request);
        //校验账号是否登录
        checkAccountHasLogin(request);
        //校验权限
        checkPermission(handlerMethod);

        return true;
    }



    private void checkToken(HttpServletRequest request) throws Exception {
        String requestURI = request.getRequestURI();
        LogWriter.info("接口token校验，请求地址：{}", requestURI);
        String token = request.getHeader("token");
        LogWriter.info("token:{}", token);
        String appId = request.getHeader("appId");
        LogWriter.info("appId:{}", appId);
        String openId = request.getHeader("openId");
        LogWriter.info("openId:{}", openId);
        String unionId = request.getHeader("unionId");
        LogWriter.info("unionId:{}", unionId);
        if (StringUtils.isBlank(token) || StringUtils.isBlank(appId) || StringUtils.isBlank(openId)) {
            for (String url : URLConstant.TOKEN_NOT_AUTH_URL.keySet()){
                if (requestURI.indexOf(url) >= 0) {
                    return;
                }
            }
            LogWriter.warn("AuthInterceptor.preHandle:token为空");
            throw new FUException(ResponseCodeEnum.PERMISSION_EXCEPTION);
        }
        LoginResp loginInfo = tokenService.getRedisInfo(token);
        if (loginInfo == null) {
            LogWriter.warn("token获取登录信息为空");
            throw new FUException(ResponseCodeEnum.PERMISSION_EXCEPTION);
        }
        if (!appId.equals(loginInfo.getAppId())) {
            LogWriter.warn("token登录信息: appId is not correct");
            throw new FUException(ResponseCodeEnum.PERMISSION_EXCEPTION);
        }
        if (!openId.equals(loginInfo.getOpenid())) {
            LogWriter.warn("token登录信息: openId is not correct");
            throw new FUException(ResponseCodeEnum.PERMISSION_EXCEPTION);
        }
        if (StringUtils.isNotBlank(unionId) && !unionId.equals(loginInfo.getUnionid())) {
            LogWriter.warn("token登录信息: unionId is not correct");
            throw new FUException(ResponseCodeEnum.PERMISSION_EXCEPTION);
        }
        if (!loginInfo.isWechatAuthed()) {
            LogWriter.warn("token登录信息: isWechatAuthed is not true");
            throw new FUException(ResponseCodeEnum.PERMISSION_EXCEPTION);
        }
        if(System.currentTimeMillis() > loginInfo.getExpireTime()){
            LogWriter.warn("token登录信息: time is expire");
            throw new FUException(ResponseCodeEnum.PERMISSION_EXCEPTION);
        }
        //获取并设置用户ip
        loginInfo.setUserIp(NetworkUtils.getIpAddress(request));
        LoginConstat.setLoginToken(loginInfo);
    }

    private void checkAccountHasLogin(HttpServletRequest request) throws Exception {
        for (String url : URLConstant.ACCOUNT_NOT_lOGIN_URL.keySet()){
            if (request.getRequestURI().indexOf(url) >= 0) {
                return;
            }
        }
        if(!LoginConstat.getLoginToken().isAccountLogin()){
            throw new FUException(ResponseCodeEnum.ACCOUNT_HAS_NOT_LOGIN_ERROR);
        }
    }

    private void checkPermission(HandlerMethod handlerMethod) throws Exception {
        RequiresPermissions annotation = handlerMethod.getMethodAnnotation(RequiresPermissions.class);
        if (annotation == null) {
            return;
        }
        LoginResp loginInfo = LoginConstat.getLoginToken();
        boolean allMchntUserType = annotation.allMchntUserType();
        if(allMchntUserType
            && loginInfo.getUserType().equals(UserTypeEnum.CUSTOMER.getCode())){
            LogWriter.warn("用户权限校验失败，客户类型不允许访问");
            throw new FUException(ResponseCodeEnum.ROLE_PERMISSION_EXCEPTION);
        }
        UserTypeEnum[] userTypes = annotation.userType();
        if (userTypes.length > 0 && !Arrays.asList(userTypes).contains(UserTypeEnum.getByCode(loginInfo.getUserType()))) {
            LogWriter.warn("用户权限校验失败");
            throw new FUException(ResponseCodeEnum.ROLE_PERMISSION_EXCEPTION);
        }
        EmployeeRoleTypeEnum[] employeeRoleTypes = annotation.employeeRoleType();
        if (employeeRoleTypes.length > 0 && !Arrays.asList(employeeRoleTypes).contains(EmployeeRoleTypeEnum.getByCode(loginInfo.getEmployeeRoleType()))) {
            LogWriter.warn("用户权限校验失败");
            throw new FUException(ResponseCodeEnum.ROLE_PERMISSION_EXCEPTION);
        }
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }


}
