package com.fuiou.dips.enums;

import com.fuiou.dips.utils.StringUtil;

import java.util.Arrays;

public enum TradeTypeEnum {

    POSITIVE_TXN("1", "正交易", "收款"),
    COUNTER_TXN("2", "反交易", "退款");

    private String code;
    private String name;
    private String desc;

    TradeTypeEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public String getCode() {

        return code == null ? null : code.trim();
    }

    public String getName() {

        return name == null ? null : name.trim();
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        return Arrays.stream(values()).filter(e -> StringUtil.isNotBlank(code) && e.getCode().equals(code.trim()))
                .findFirst().map(TradeTypeEnum::getDesc).orElse(code);
    }
}
