package com.fuiou.dips.framework.filter;

import com.fuiou.dips.utils.LogWriter;
import org.apache.log4j.Logger;
import org.owasp.validator.html.AntiSamy;
import org.owasp.validator.html.CleanResults;
import org.owasp.validator.html.Policy;
import org.owasp.validator.html.PolicyException;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

public class XssCleaner {

	private static Logger logger = Logger.getLogger(XssCleaner.class);
	private static Policy policy = null;
	
	static {
		String path = XssCleaner.class.getResource("antisamy-anythinggoes-1.4.4.xml").getFile();
		try {
			path = URLDecoder.decode(path,"UTF-8");
			logger.info("policy_filepath:" + path);
			if (path.startsWith("file"))path = path.substring(6);
			policy = Policy.getInstance(path);
		} catch (PolicyException e) {
			LogWriter.error("异常",e);
			throw new IllegalArgumentException("初始化policy异常");
		} catch (UnsupportedEncodingException e) {
			LogWriter.error("异常",e);
			throw new IllegalArgumentException("转码异常");
		}
	}
	
	/**
	 * 过滤非法字符
	 * @param value
	 * @return
	 */
	public  static String xssClean(String value) {
		AntiSamy antiSamy = new AntiSamy();
		try {
			final CleanResults cr = antiSamy.scan(value, policy);
			return cr.getCleanHTML();
		} catch (Exception e) {
			LogWriter.error("异常",e);
			return "";
		}
	}
}
