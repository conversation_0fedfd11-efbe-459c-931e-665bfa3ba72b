package com.fuiou.dips.swt.utils;

import com.fuiou.dips.utils.LogWriter;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;


public class BcdUtil {
    private static final Logger logger = Logger.getLogger(BcdUtil.class);

    /**
     * HEX 字符串 转 字节码
     * <p>
     * 长度为单数则最后一位舍弃
     * </p>
     *
     * @param hex
     * @return
     */
    public static byte[] hexStringToByte(String hex) {

        byte[] result = new byte[hex.length() / 2];
        int index;
        for (int i = 0; i < hex.length() / 2; i++) {
            result[i] = (byte) (toByte(hex.charAt(index = i * 2)) << 4 | toByte(hex
                    .charAt(index + 1)));
        }
        return result;
    }

    private static byte[] hex = new byte[128];
    static {
        synchronized (hex) {
            for (int i = '0', j = 0; i <= '9'; i++)
                hex[i] = (byte) j++;
            for (int i = 'A', j = 10; i <= 'F'; i++)
                hex[i] = (byte) j++;
            for (int i = 'a', j = 10; i <= 'F'; i++)
                hex[i] = (byte) j++;
        }
    }

    public static byte toByte(char c) {
        return hex[c];
    }

    public static final String[] hexStr = "0,1,2,3,4,5,6,7,8,9,A,B,C,D,E,F"
            .split(",");

    /**
     * 生成
     *
     * @param bArray
     * @return
     */
    public static final String b2Hex(final byte[] bArray) {

        if (bArray == null)
            return null;
        StringBuilder sb = new StringBuilder(bArray.length);
        for (byte b : bArray)
            sb.append(hexStr[b >>> 4 & 0xf]).append(hexStr[b & 0xf]);

        return sb.toString();
    }

    /**
     * 根据byte转换成二进制，8位
     *
     * @param b
     * @return
     */
    public static String byteToBinaryString(byte b) {

        StringBuilder sb = new StringBuilder();
        for (int i = 7; i >= 0; i--)
            sb.append(((b & 1 << i) >>> i == 0) ? "0" : "1");

        return sb.toString();
    }

    /**
     * 根据int值转换成二进制值，32位
     *
     * @param b
     * @return
     */
    public static String intToBinaryString(int b) {

        StringBuilder sb = new StringBuilder();
        for (int i = 31; i >= 0; i--)
            sb.append(((b & 1 << i) >>> i == 0) ? "0" : "1");

        return sb.toString();
    }

    public static final Object bytesToObject(byte[] bytes) throws IOException,
            ClassNotFoundException {
        ObjectInputStream oi = new ObjectInputStream(new ByteArrayInputStream(
                bytes));
        Object o = oi.readObject();
        oi.close();
        return o;
    }

    public static final byte[] objectToBytes(Serializable s) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ObjectOutputStream ot = new ObjectOutputStream(out);
        ot.writeObject(s);
        ot.flush();
        ot.close();
        return out.toByteArray();
    }

    public static final String objectToHexString(Serializable s)
            throws IOException {
        return b2Hex(objectToBytes(s));
    }

    public static final Object hexStringToObject(String hex)
            throws IOException, ClassNotFoundException {
        return bytesToObject(hexStringToByte(hex));
    }

    public static String bcd2Str(byte[] bytes) {
        StringBuffer temp = new StringBuffer(bytes.length * 2);

        for (int i = 0; i < bytes.length; i++) {
            temp.append((byte) ((bytes[i] & 0xf0) >>> 4));
            temp.append((byte) (bytes[i] & 0x0f));
        }
        return temp.toString().substring(0, 1).equalsIgnoreCase("0") ? temp
                .toString().substring(1) : temp.toString();
    }

    public static byte[] str2Bcd(String asc) {
        int len = asc.length();
        int mod = len % 2;

        if (mod != 0) {
            asc = "0" + asc;
            len = asc.length();
        }

        byte abt[] = new byte[len];
        if (len >= 2) {
            len = len / 2;
        }

        byte bbt[] = new byte[len];
        abt = asc.getBytes();
        int j, k;

        for (int p = 0; p < asc.length() / 2; p++) {
            if ((abt[2 * p] >= '0') && (abt[2 * p] <= '9')) {
                j = abt[2 * p] - '0';
            } else if ((abt[2 * p] >= 'a') && (abt[2 * p] <= 'z')) {
                j = abt[2 * p] - 'a' + 0x0a;
            } else {
                j = abt[2 * p] - 'A' + 0x0a;
            }

            if ((abt[2 * p + 1] >= '0') && (abt[2 * p + 1] <= '9')) {
                k = abt[2 * p + 1] - '0';
            } else if ((abt[2 * p + 1] >= 'a') && (abt[2 * p + 1] <= 'z')) {
                k = abt[2 * p + 1] - 'a' + 0x0a;
            } else {
                k = abt[2 * p + 1] - 'A' + 0x0a;
            }

            int a = (j << 4) + k;
            byte b = (byte) a;
            bbt[p] = b;
        }
        return bbt;
    }

    public static String MD5EncodeToHex(String origin) {
        return b2Hex(MD5Encode(origin));
    }

    public static byte[] MD5Encode(String origin) {
        return MD5Encode(origin.getBytes());
    }

    public static byte[] MD5Encode(byte[] bytes) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
            return md.digest(bytes);
        } catch (NoSuchAlgorithmException e) {
            LogWriter.error("异常",e);
            return new byte[0];
        }
    }

    public static String encryptBy3DES(byte[] plaintext, byte[] bytesKey) {

        logger.debug("bytesKey length : "
                + (bytesKey == null ? -1 : bytesKey.length));
        if (bytesKey == null)
            return null;

        String cipherText = null;
        try {

            Cipher cipher = Cipher.getInstance("DESede/ECB/NOPadding");
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(bytesKey,
                    "DESede"));
            cipherText = BcdUtil.b2Hex(cipher.doFinal(plaintext));

            logger.debug("encrypted key : " + cipherText);

        } catch (Exception ex) {
            logger.error("encryptBy3DES方法异常", ex);
        }
        return cipherText;
    }

    /**
     * 将加密后的密文（HEX格式）单字符再转换成16进制格式显示...
     *
     * @param plaintext
     * @param bytesKey
     * @return
     */
    public static String encryptBy3DES4Hex(byte[] plaintext, byte[] bytesKey) {

        logger.debug("bytesKey length : "
                + (bytesKey == null ? -1 : bytesKey.length));
        if (bytesKey == null)
            return null;

        StringBuilder sb = new StringBuilder();
        try {

            Cipher cipher = Cipher.getInstance("DESede/ECB/NOPadding");
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(bytesKey,
                    "DESede"));
            String cipherText = BcdUtil.b2Hex(cipher.doFinal(plaintext));
            char c;
            for (int i = 0; i < cipherText.length(); i++) {
                c = cipherText.charAt(i);
                logger.debug("encrypted key 4 ASCii cipherText[" + i + "]:" + c
                        + "==" + Integer.toHexString(c));
                sb.append(Integer.toHexString(c));
            }
            logger.debug("encrypted key 4 Hex:" + sb.toString());
        } catch (Exception ex) {
            logger.error("encryptBy3DES4Hex方法处理异常", ex);
        }
        return sb.toString();
    }

    public static String decryptBy3DES(byte[] encryptedtext, byte[] bytesKey) {
        logger.debug("bytesKey length : "
                + (bytesKey == null ? -1 : bytesKey.length));
        if (bytesKey == null)
            return null;

        String cipherText = null;
        try {
            Cipher cipher = Cipher.getInstance("DESede/ECB/NOPadding");
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(bytesKey,
                    "DESede"));
            cipherText = new String(cipher.doFinal(encryptedtext));
            logger.debug(cipherText);
        } catch (Exception ex) {
            logger.error("decryptBy3DES方法处理异常", ex);
        }
        return cipherText;
    }

    public static byte[] process(String pinPlain, String pinCardS) {
        byte arrAccno[] = BcdUtil.str2Bcd(pinCardS);
        byte arrPin[] = BcdUtil.str2Bcd(pinPlain);
        byte arrRet[] = new byte[8];
        Arrays.fill(arrRet, (byte) 0x00);
        for (int i = 0; i < Math.min(arrAccno.length, arrPin.length); i++) {
            arrRet[i] = (byte) (arrPin[i] ^ arrAccno[i]);
        }
        return arrRet;
    }

    // short byte array 相互转换
    public static byte[] shortToByteArray(short s) {
        byte[] shortBuf = new byte[2];
        for (int i = 0; i < 2; i++) {
            int offset = (shortBuf.length - 1 - i) * 8;
            shortBuf[i] = (byte) ((s >>> offset) & 0xff);
        }
        return shortBuf;
    }

    public static final int b2Short(byte[] b) {
        return (b[0] << 8) + (b[1] & 0xFF);
    }

    /**
     * 压缩BCD码
     *
     * @param data
     * @return
     */
    public static byte[] compressionBCD(byte[] data) {

        if (data == null)
            return null;

        byte[] result = new byte[data.length / 2
                + (data.length % 2 == 0 ? 0 : 1)];

        for (int i = 0, j = 0; i < result.length && j < data.length; i++)
            result[i] = (byte) (data[j++] << 4 | (j + 1 < data.length ? data[j++]
                    : 0));

        return result;
    }

    /**
     * 解压缩BCD码
     *
     * @param data
     * @return
     */
    public static byte[] uncompressionBCD(byte[] data) {

        if (data == null)
            return null;

        byte[] result = new byte[data.length * 2];

        for (int i = 0, j = 0; i < data.length; i++) {
            result[j++] = (byte) (data[i] >>> 4 & 0xff);
            result[j++] = (byte) (data[i] & 0xff);
        }

        if (result[result.length - 1] == 0)
            result = Arrays.copyOf(result, result.length - 1);

        return result;
    }

    /**
     * 打印HEX
     *
     * @param str
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String str2hex(String str, String charsetName) throws UnsupportedEncodingException {

        if (StringUtils.isBlank(str))
            return "";

        StringBuilder sb = new StringBuilder();

        for (byte b : str.getBytes(charsetName))
            sb.append(hexStr[b >>> 4 & 0xf]).append(hexStr[b & 0xf]);

        return sb.toString();
    }

    /**
     * 编码 MAC
     *
     * @param body
     * @return
     */
    public static byte[] mac(byte[] body, byte[] key) {

        // body 为 消息类型 到 63域 之前的内容
        if (body == null || body.length < 1)
            throw new NullPointerException("mac value is required!!");

        // 异或结果
        byte[] tar = new byte[] { 0, 0, 0, 0, 0, 0, 0, 0 };

        // 异或
        for (int j = 0; j < body.length;)
            for (int i = 0; i < 8; i++)
                tar[i] ^= (byte) (j < body.length ? body[j++] : 0);

        // 结果转换成16 个HEXDECIMAL
        byte[] hexFore = new byte[] { 0, 0, 0, 0, 0, 0, 0, 0 };
        byte[] hexAfter = new byte[] { 0, 0, 0, 0, 0, 0, 0, 0 };

        for (int i = 0, j = i * 2; i < 4; i++, j = i * 2) {
            hexFore[j] = (byte) ((tar[i] & 0xf0) >>> 4);
            hexFore[j + 1] = (byte) (tar[i] & 0xf);
        }
        for (int i = 4, j = (i - 4) * 2; i < 8; i++, j = (i - 4) * 2) {
            hexAfter[j] = (byte) ((tar[i] & 0xf0) >>> 4);
            hexAfter[j + 1] = (byte) (tar[i] & 0xf);
        }

        // 取前8 个字节用MAK加密
        byte[] encode;
        try {
            encode = DESUtil.encrypt(hexFore, key);

            if (logger.isDebugEnabled())
                logger.debug("生成MAC第一次加密生成密文 : [data : "
                        + DESUtil.byte2hex(hexFore) + "; key : "
                        + DESUtil.byte2hex(key) + "; result : "
                        + DESUtil.byte2hex(encode) + "; str : "
                        + b2Hex(DESUtil.byte2hex(encode).substring(0, 8).getBytes()) + "]");

        } catch (Exception e) {
            logger.error("生成MAC第一次加密出错[data : " + DESUtil.byte2hex(hexFore)
                    + "; key : " + key + "]", e);
            return null;
        }

        // 将加密后的结果与后8 个字节异或
        for (int i = 0; i < hexAfter.length; i++) {
            hexAfter[i] ^= encode[i];
        }

        // 用异或的结果再进行一次单倍长密钥算法运算eMAK
        try {
            encode = DESUtil.encrypt(hexAfter, key);

            if (logger.isDebugEnabled())
                logger.debug("生成MAC第二次加密生成密文 : [data : " + b2Hex(hexAfter)
                        + "; key : " + DESUtil.byte2hex(key) + "; result : "
                        + DESUtil.byte2hex(encode) + "; str : "
                        + b2Hex(DESUtil.byte2hex(encode).substring(0, 8).getBytes()) +"]");

        } catch (Exception e) {
            logger.error("生成MAC第二次加密出错[data : " + DESUtil.byte2hex(hexAfter)
                    + "; key : " + key + "]", e);
            return null;
        }
        // 结果转换成16 个HEXDECIMAL
        // 前8位为MAC值
        return DESUtil.byte2hex(encode).substring(0, 8).getBytes();
    }

    public static final String hexStrToAsc(String hexString){
        String ascStr = "";
        byte[] bytes = new byte[hexString.length()/2];
        for(int i=0;i<bytes.length;i++){
            bytes[i] = (byte)(0xff & Integer.parseInt(hexString.substring(i*2, i*2+2),16));
        }
        try {
            ascStr = new String(bytes,"utf-8");
        } catch (Exception e) {
            LogWriter.error("异常",e);
        }
        return ascStr;
    }

    public static final String bytesToHexString(byte[] bArray) {
        StringBuffer sb = new StringBuffer(bArray.length);
        String sTemp;
        for (int i = 0; i < bArray.length; i++) {
            sTemp = Integer.toHexString(bArray[i] & 0xFF);
            if (sTemp.length() < 2)
                sb.append('0');
            sb.append(sTemp.toUpperCase());
        }
        return sb.toString();
    }
}
