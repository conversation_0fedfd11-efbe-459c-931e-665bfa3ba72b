package com.fuiou.dips.services;

import com.alibaba.fastjson.JSONArray;
import com.fuiou.dboffset.soa.data.*;
import com.fuiou.dboffset.soa.rpc.CfgdbOffsetHandler;
import com.fuiou.dips.persist.beans.MchntAcntLoginInfBean;
import com.fuiou.dips.persist.beans.OperatorInfBean;
import com.fuiou.dips.utils.JsonUtil;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.MapUtils;
import com.github.pagehelper.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class CfgdbService {

    public static final String PLAT_ID = "DIPS";

    @Resource
    private CfgdbOffsetHandler cfgdbOffsetHandler;


    @Resource(name = "jdbcTemplate_cfgdb")
    private JdbcTemplate jdbcTemplate;

    public TermDbData getTermById(String fyTermId) {
        try {
            LogWriter.info(this, String.format("通过富友终端号调用dboffset 查询终端信息，参数为：fyTermId=%s", fyTermId));
            if (StringUtil.isEmpty(fyTermId)) {
                LogWriter.info(this, String.format("通过富友终端号调用dboffset 查询终端信息  参数为空。"));
                return null;
            }
            fyTermId = fyTermId.trim();
            TermDbData result = cfgdbOffsetHandler.getTermById(fyTermId);
            LogWriter.info(this, String.format("通过富友终端号调用dboffset 查询终端信息 ,result=%s",
                    JsonUtil.bean2Json(result)));
            return result;
        } catch (Exception e) {
            LogWriter.error(this, String.format("通过富友终端号调用dboffset 查询终端信息，发生异常"), e);
            return null;
        }
    }

    public TermDbData getTermBySerialNo(String termSerialNo) {
        try {
            LogWriter.info(this,
                    String.format("通过富友终端序列号调用dboffset 查询终端信息，参数为：termSerialNo=%s", termSerialNo));
            if (StringUtil.isEmpty(termSerialNo)) {
                LogWriter.info(this, String.format("通过富友终端序列号调用dboffset 查询终端信息  参数为空。"));
                return null;
            }
            termSerialNo = termSerialNo.trim();
            TermDbData result = cfgdbOffsetHandler.getTermBySerialNo(termSerialNo);
            LogWriter.info(this, String.format("通过富友终端序列号调用dboffset 查询终端信息 ,result=%s",
                    JsonUtil.bean2Json(result)));
            return result;
        } catch (Exception e) {
            LogWriter.error(this, String.format("通过富友终端序列号调用dboffset 查询终端信息，发生异常"), e);
            return null;
        }
    }

    public InsMchntDbData getMchntByMchntCd(String mchntCd) {
        try {
            LogWriter.info(this, String.format("通过富友商户号调用dboffset 查询商户信息，参数为：mchntCd=%s", mchntCd));
            if (StringUtil.isEmpty(mchntCd)) {
                LogWriter.info(this, String.format("通过富友商户号调用dboffset 查询商户信息 参数为空。"));
                return null;
            }
            mchntCd = mchntCd.trim();
            InsMchntDbData result = cfgdbOffsetHandler.getMchntByMchntCd(mchntCd);
            LogWriter.info(this, String.format("通过富友商户号调用dboffset 查询商户信息 ,result=%s",
                    JsonUtil.bean2Json(result)));
            return result;
        } catch (Exception e) {
            LogWriter.error(this, String.format("通过富友商户号调用dboffset 查询商户信息，发生异常"), e);
            return null;
        }
    }

    public InsMchntDbData getMchntByInsCd(String insCd) {
        try {
            LogWriter.info(this, String.format("通过机构号调用dboffset 查询商户信息，参数为：insCd=%s", insCd));
            if (StringUtil.isEmpty(insCd)) {
                LogWriter.info(this, String.format("通过机构号调用dboffset 查询商户信息 参数为空。"));
                return null;
            }
            insCd = insCd.trim();
            InsMchntDbData result = cfgdbOffsetHandler.getMchntByInsCd(insCd);
            LogWriter.info(this,
                    String.format("通过机构号调用dboffset 查询商户信息 ,result=%s", JsonUtil.bean2Json(result)));
            return result;
        } catch (Exception e) {
            LogWriter.error(this, String.format("通过机构号调用dboffset 查询商户信息，发生异常"), e);
            return null;
        }
    }


    /**
     * 根据用户名密码查询商户账号信息
     *
     * @param userName
     * @param password
     * @return
     */
    public MchntAcntLoginInfBean getMchntAcntLogInfForLogin(String userName, String password) {
        try {
            String sql = "SELECT " + " login_id," + " mchnt_cd ," + " pwd," + " name, " + " mobile, " + " role_id," +
                    " user_st, " + " user_tp, " + " relate_store_user_id, group_id " + " FROM t_mchnt_acnt_login_inf " +
                    " WHERE login_id = ? AND pwd = ? AND user_st = '1' WITH UR";
            QueryReq queryReq = new QueryReq();
            queryReq.setPlatId(PLAT_ID);
            queryReq.setSqlStr(sql);
            Object[] params = new Object[2];
            params[0] = userName;
            params[1] = password;
            queryReq.setParams(params);
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,queryReq=%s", JsonUtil.bean2Json(queryReq)));
            Map<String, Object> result = cfgdbOffsetHandler.commonQueryPrepare(queryReq);
            if (result == null) {
                LogWriter.info(this, String.format("查询t_mchnt_acnt_login_inf失败,数据库查询结果为空"));
                return null;
            }
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,resultMap=%s", JSONArray.toJSONString(result)));
            MchntAcntLoginInfBean user = MapUtils.mapToEntity(result, MchntAcntLoginInfBean.class);
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,MchntAcntLoginInfBean=%s", JSONArray.toJSONString(user)));
            return user;
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询t_mchnt_acnt_login_inf异常,e=%s", e.getMessage()), e);
            return null;
        }
    }

    /**
     * 根据手机号查询商户账号信息
     * @param phone
     * @return
     */
    public List<MchntAcntLoginInfBean> getMchntAcntLogInfForMobileLogin(String phone) {
        try {
            String sql = "SELECT " + " login_id," + " mchnt_cd ," + " pwd," + " name, " + " mobile, " + " role_id," +
                    " user_st, " + " user_tp, " + " relate_store_user_id, group_id " + " FROM t_mchnt_acnt_login_inf " +
                    " WHERE mobile = ? AND user_st = '1' WITH UR";
            QueryReq queryReq = new QueryReq();
            queryReq.setPlatId(PLAT_ID);
            queryReq.setSqlStr(sql);
            Object[] params = new Object[1];
            params[0] = phone;
            queryReq.setParams(params);
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,queryReq=%s", JsonUtil.bean2Json(queryReq)));
            List<Map<String, Object>> resultList = cfgdbOffsetHandler.commonQueryListPrepare(queryReq);
            if (resultList == null || resultList.isEmpty()) {
                LogWriter.info(this, String.format("查询t_mchnt_acnt_login_inf失败,数据库查询结果为空"));
                return null;
            }
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,resultMap=%s", JSONArray.toJSONString(resultList)));
            List<MchntAcntLoginInfBean> userList = new ArrayList<MchntAcntLoginInfBean>();
            for (Map<String, Object> result : resultList){
                userList.add(MapUtils.mapToEntity(result, MchntAcntLoginInfBean.class));
            }
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,MchntAcntLoginInfBean=%s", JSONArray.toJSONString(userList)));
            return userList;
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询t_mchnt_acnt_login_inf异常,e=%s", e.getMessage()), e);
            return null;
        }
    }

    /**
     * 根据手机号查询商户账号信息
     * @param phone
     * @return
     */
    public List<MchntAcntLoginInfBean> getMchntAcntLogInfForMobileLoginWithMchnt(String phone) {
        try {
            String sql = "SELECT  t1.login_id, t1.mchnt_cd , t1.pwd, t1.name,  t1.mobile,  t1.role_id, t1.user_st,  t1.user_tp,  t1.relate_store_user_id , t3.INS_NAME_CN, t3.INS_NM_JC_CN,  t1.group_id " +
                    "FROM t_mchnt_acnt_login_inf t1, t_ins_mchnt_inf t3  " +
                    "WHERE t1.mchnt_cd = t3.mchnt_cd AND t1.mobile = ? AND t1.user_st = '1' AND t3.ROW_TP = '1' AND t3.ROW_ST = '1' WITH UR";
            QueryReq queryReq = new QueryReq();
            queryReq.setPlatId(PLAT_ID);
            queryReq.setSqlStr(sql);
            Object[] params = new Object[1];
            params[0] = phone;
            queryReq.setParams(params);
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,queryReq=%s", JsonUtil.bean2Json(queryReq)));
            List<Map<String, Object>> resultList = cfgdbOffsetHandler.commonQueryListPrepare(queryReq);
            if (resultList == null || resultList.isEmpty()) {
                LogWriter.info(this, String.format("查询t_mchnt_acnt_login_inf失败,数据库查询结果为空"));
                return null;
            }
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,resultMap=%s", JSONArray.toJSONString(resultList)));
            List<MchntAcntLoginInfBean> userList = new ArrayList<MchntAcntLoginInfBean>();
            for (Map<String, Object> result : resultList){
                userList.add(MapUtils.mapToEntity(result, MchntAcntLoginInfBean.class));
            }
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,MchntAcntLoginInfBean=%s", JSONArray.toJSONString(userList)));
            return userList;
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询t_mchnt_acnt_login_inf异常,e=%s", e.getMessage()), e);
            return null;
        }
    }

    public  MchntAcntLoginInfBean  getMchntAcntLogInfByLoginId(String loginId) {
        try {
            String sql = "SELECT " + " login_id," + " mchnt_cd ," + " pwd," + " name, " + " mobile, " + " role_id," +
                    " user_st, " + " user_tp, " + " relate_store_user_id,  group_id " + " FROM t_mchnt_acnt_login_inf " +
                    " WHERE login_id = ?  AND user_st = '1' WITH UR";
            QueryReq queryReq = new QueryReq();
            queryReq.setPlatId(PLAT_ID);
            queryReq.setSqlStr(sql);
            Object[] params = new Object[1];
            params[0] = loginId;
            queryReq.setParams(params);
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,queryReq=%s", JsonUtil.bean2Json(queryReq)));
            Map<String, Object> result = cfgdbOffsetHandler.commonQueryPrepare(queryReq);
            if (result == null) {
                LogWriter.info(this, String.format("查询t_mchnt_acnt_login_inf失败,数据库查询结果为空"));
                return null;
            }
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,resultMap=%s", JSONArray.toJSONString(result)));
            MchntAcntLoginInfBean user = MapUtils.mapToEntity(result, MchntAcntLoginInfBean.class);
            LogWriter.info(this,
                    String.format("查询t_mchnt_acnt_login_inf,MchntAcntLoginInfBean=%s", JSONArray.toJSONString(user)));
            return user;
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询t_mchnt_acnt_login_inf异常,e=%s", e.getMessage()), e);
            return null;
        }
    }

    /**
     * 获取操作员信息
     *
     * @param userName
     * @param password
     * @return
     */
    public OperatorInfBean getOperatorInfForLogin(String userName, String password) {
        try {
            String sql =
                    "SELECT  login_id, login_pwd, ins_cd, mchnt_cd,  user_st,  user_name_cn, mobile_no,  email,  " +
                            "role_id  " + " FROM t_operator_inf " +
                            " WHERE login_id = ? AND login_pwd = ? AND user_st = '1' WITH UR";
            QueryReq queryReq = new QueryReq();
            queryReq.setPlatId(PLAT_ID);
            queryReq.setSqlStr(sql);
            Object[] params = new Object[2];
            params[0] = userName;
            params[1] = password;
            queryReq.setParams(params);
            LogWriter.info(this, String.format("查询t_operator_inf,queryReq=%s", JsonUtil.bean2Json(queryReq)));
            Map<String, Object> result = cfgdbOffsetHandler.commonQueryPrepare(queryReq);
            if (result == null) {
                LogWriter.info(this, String.format("查询t_operator_inf失败,数据库查询结果为空"));
                return null;
            }
            LogWriter.info(this, String.format("查询t_operator_inf,resultMap=%s", JSONArray.toJSONString(result)));
            OperatorInfBean user = MapUtils.mapToEntity(result, OperatorInfBean.class);
            LogWriter.info(this, String.format("查询t_operator_inf,OperatorInfBean=%s", JSONArray.toJSONString(user)));
            return user;
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询t_operator_inf异常,e=%s", e.getMessage()), e);
            return null;
        }
    }

    /**
     * 获取操作员信息
     *
     * @param userName
     * @return
     */
    public OperatorInfBean getOperatorInfForLoginId(String userName) {
        try {
            String sql =
                    "SELECT  login_id, login_pwd, ins_cd, mchnt_cd,  user_st,  user_name_cn, mobile_no,  email,  " +
                            "role_id  " + " FROM t_operator_inf " +
                            " WHERE login_id = ? AND user_st = '1' WITH UR";
            QueryReq queryReq = new QueryReq();
            queryReq.setPlatId(PLAT_ID);
            queryReq.setSqlStr(sql);
            Object[] params = new Object[1];
            params[0] = userName;
            queryReq.setParams(params);
            LogWriter.info(this, String.format("查询t_operator_inf,queryReq=%s", JsonUtil.bean2Json(queryReq)));
            Map<String, Object> result = cfgdbOffsetHandler.commonQueryPrepare(queryReq);
            if (result == null) {
                LogWriter.info(this, String.format("查询t_operator_inf失败数据库查询结果为空"));
                return null;
            }
            LogWriter.info(this, String.format("查询t_operator_inf,resultMap=%s", JSONArray.toJSONString(result)));
            OperatorInfBean user = MapUtils.mapToEntity(result, OperatorInfBean.class);
            LogWriter.info(this, String.format("查询t_operator_inf,OperatorInfBean=%s", JSONArray.toJSONString(user)));
            return user;
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询t_operator_inf异常,e=%s", e.getMessage()), e);
            return null;
        }
    }

    /**
     * 获取操作员信息
     *
     * @param phone
     * @return
     */
    public List<OperatorInfBean> getOperatorInfForMobileLogin(String phone) {
        try {
            //t_operator_inf表的bind_mobile字段用于登录，查询出来可能涉及多条
            String sql =
                    "SELECT  login_id, login_pwd, ins_cd, mchnt_cd,  user_st,  user_name_cn, mobile_no,  email,  " +
                            "role_id  " + " FROM t_operator_inf " +
                            " WHERE bind_mobile = ? AND user_st = '1' WITH UR";
            QueryReq queryReq = new QueryReq();
            queryReq.setPlatId(PLAT_ID);
            queryReq.setSqlStr(sql);
            Object[] params = new Object[1];
            params[0] = phone;
            queryReq.setParams(params);
            LogWriter.info(this, String.format("查询t_operator_inf,queryReq=%s", JsonUtil.bean2Json(queryReq)));
            List<Map<String, Object>> resultList = cfgdbOffsetHandler.commonQueryListPrepare(queryReq);
            if (resultList == null || resultList.isEmpty()) {
                LogWriter.info(this, String.format("查询t_operator_inf失败,数据库查询结果为空"));
                return null;
            }
            LogWriter.info(this, String.format("查询t_operator_inf,resultMap=%s", JSONArray.toJSONString(resultList)));
            List<OperatorInfBean> userList = new ArrayList<OperatorInfBean>();
            for(Map<String, Object> result : resultList){
                userList.add(MapUtils.mapToEntity(result, OperatorInfBean.class));
            }
            LogWriter.info(this, String.format("查询t_operator_inf,OperatorInfBean=%s", JSONArray.toJSONString(userList)));
            return userList;
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询t_operator_inf异常,e=%s", e.getMessage()), e);
            return null;
        }
    }

    public List<OperatorInfBean> getOperatorInfForMobileLoginWithSubMchnt(String phone) {
        try {
            //t_operator_inf表的bind_mobile字段用于登录，查询出来可能涉及多条
            String sql =
                    "SELECT  t1.login_id, t1.login_pwd, t1.ins_cd, t1.mchnt_cd,  t1.user_st,  t1.user_name_cn, t1.mobile_no,  t1.email, " +
                            "                            t1.role_id, t3.INS_NAME_CN, t3.INS_NM_JC_CN   FROM t_operator_inf t1, t_ins_mchnt_inf t3 " +
                            "                             WHERE t1.mchnt_cd = t3.mchnt_cd AND t1.INS_CD = t3.INS_CD AND t1.bind_mobile = ? AND t1.user_st = '1' AND t3.ROW_TP = '1' AND t3.ROW_ST = '1' " +
                            " UNION " +
                            " SELECT t1.login_id, t1.login_pwd, t1.ins_cd, t2.mchnt_cd,  t1.user_st,  t1.user_name_cn, t1.mobile_no,  t1.email, " +
                            "                            t1.role_id, t3.INS_NAME_CN, t3.INS_NM_JC_CN FROM t_operator_inf t1 , t_sub_mchnt_acnt t2 , t_ins_mchnt_inf t3 " +
                            "                            WHERE  t1.mchnt_cd = t3.mchnt_cd  AND t1.mchnt_cd = t2.ROOT_MCHNT_CD AND t1.bind_mobile = ? AND t1.user_st = '1' AND t3.ROW_TP = '1' AND t3.ROW_ST = '1'  " +
                            "   WITH UR    ";
            QueryReq queryReq = new QueryReq();
            queryReq.setPlatId(PLAT_ID);
            queryReq.setSqlStr(sql);
            Object[] params = new Object[2];
            params[0] = phone;
            params[0] = phone;
            queryReq.setParams(params);
            LogWriter.info(this, String.format("查询t_operator_inf,queryReq=%s", JsonUtil.bean2Json(queryReq)));
            List<Map<String, Object>> resultList = cfgdbOffsetHandler.commonQueryListPrepare(queryReq);
            if (resultList == null || resultList.isEmpty()) {
                LogWriter.info(this, String.format("查询t_operator_inf失败,数据库查询结果为空"));
                return null;
            }
            LogWriter.info(this, String.format("查询t_operator_inf,resultMap=%s", JSONArray.toJSONString(resultList)));
            List<OperatorInfBean> userList = new ArrayList<OperatorInfBean>();
            for(Map<String, Object> result : resultList){
                userList.add(MapUtils.mapToEntity(result, OperatorInfBean.class));
            }
            LogWriter.info(this, String.format("查询t_operator_inf,OperatorInfBean=%s", JSONArray.toJSONString(userList)));
            return userList;
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询t_operator_inf异常,e=%s", e.getMessage()), e);
            return null;
        }
    }

    /**
     * 按主商户号查询子商户
     *
     * @param mchntCd
     * @return
     */
    public List<InsMchntDbData> getSubMchntAcntInfo(String mchntCd) {
        try {
            String sql = "SELECT t1.INS_CD, t1.INS_TP, t1.MCHNT_CD, t1.MCHNT_TP, t1.INS_NAME_CN, t1.INS_NM_JC_CN " +
                    " FROM T_INS_MCHNT_INF t1, t_sub_mchnt_acnt t2 where " +
                    " t1.ROW_TP = '1' AND t2.ROW_ST = '1' and t2.row_tp = '1' and t2.row_st = '1' and t2" +
                    ".mchnt_cd = t1.mchnt_cd and ROOT_MCHNT_CD = ? WITH UR";
            QueryReq queryReq = new QueryReq();
            queryReq.setPlatId(PLAT_ID);
            queryReq.setSqlStr(sql);
            Object[] params = new Object[1];
            params[0] = mchntCd;
            queryReq.setParams(params);
            LogWriter.info(this, String.format("查询t_sub_mchnt_acnt,queryReq=%s", JsonUtil.bean2Json(queryReq)));
            List<Map<String, Object>> result = cfgdbOffsetHandler.commonQueryListPrepare(queryReq);
            if (result == null) {
                LogWriter.info(this, String.format("查询t_sub_mchnt_acnt失败,数据库查询结果为空"));
                return null;
            }
            LogWriter.info(this, String.format("查询t_sub_mchnt_acnt,resultMap=%s", JSONArray.toJSONString(result)));
            List<InsMchntDbData> insMchntDbDataList = new ArrayList<InsMchntDbData>();
            for (Map<String, Object> map : result) {
                insMchntDbDataList.add(MapUtils.mapToEntity(map, InsMchntDbData.class));
            }
            LogWriter.info(this, String.format("查询t_sub_mchnt_acnt,InsMchntDbData=%s",
                    JSONArray.toJSONString(insMchntDbDataList)));
            return insMchntDbDataList;
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询t_operator_inf异常,e=%s", e.getMessage()), e);
            return null;
        }
    }

    public static boolean validMchntStatus(InsMchntDbData mchntDbData) {
        return mchntDbData != null && 1L == mchntDbData.getRowSt() && 1 == mchntDbData.getRowTp();
    }

    public static boolean validMchntBusiStatus(MchntBusiDbData mchntBusiByMchntCd) {
        return mchntBusiByMchntCd != null && 1L == mchntBusiByMchntCd.getRowSt() && 1 == mchntBusiByMchntCd.getRowTp();
    }

    public static boolean validTermStatus(TermDbData termDbData) {
        return termDbData != null && "01".equals(termDbData.getTmOpenState());
    }

    public static boolean validTermNameStatus(TmNameDbData tmNameDbData) {
        return tmNameDbData != null && "1".equals(tmNameDbData.getPrtOpenSt()) && 1 == tmNameDbData.getRowTp() &&
                1 == tmNameDbData.getRowSt();
    }

    public static boolean validTermNameStatusIgnorePrtOpenSt(TmNameDbData tmNameDbData) {
        return tmNameDbData != null && 1 == tmNameDbData.getRowTp() && 1 == tmNameDbData.getRowSt();
    }

    public static boolean validTermNameStatusIgnorePrtOpenSt(TmNameDbData tmNameDbData, String mchntCd) {
        return validTermNameStatusIgnorePrtOpenSt(tmNameDbData) && StringUtil.isNotEmpty(mchntCd) && mchntCd.equals(
                StringUtils.trim(tmNameDbData.getMchntCd()));
    }

    public static boolean validTermNameStatus(TmNameDbData tmNameDbData, String mchntCd) {
        return validTermNameStatus(tmNameDbData) && StringUtil.isNotEmpty(mchntCd) && mchntCd.equals(
                StringUtils.trim(tmNameDbData.getMchntCd()));
    }

    /**
     * CommonQueryList - 构建查询请求并执行
     *
     * @param sql       SQL格式字符串
     * @param logPrefix 日志前缀，用于标识查询的表或操作
     * @return 查询结果列表
     */
    public List<Map<String, Object>> executeCommonQueryListPrepare(String sql, String logPrefix, Object... params) {
        QueryReq queryReq = new QueryReq();
        queryReq.setPlatId(PLAT_ID);
        queryReq.setSqlStr(sql);
        queryReq.setParams(params);
        LogWriter.info(this, String.format("查询%s,queryReq=%s", logPrefix, JsonUtil.bean2Json(queryReq)));
        try {
            List<Map<String, Object>> resultList = cfgdbOffsetHandler.commonQueryListPrepare(queryReq);
            if (resultList != null && !resultList.isEmpty()) {
                LogWriter.info(this, String.format("查询%s成功,共%d条记录", logPrefix, resultList.size()));
                return resultList;
            } else {
                LogWriter.info(this, String.format("查询%s结果为空", logPrefix));
                return Collections.emptyList();
            }
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询%s异常: %s", logPrefix, e.getMessage()), e);
            return Collections.emptyList();
        }
    }

    /**
     * executeCommonQueryPrepare - 构建查询请求并执行
     *
     * @param sql       SQL格式字符串
     * @param logPrefix 日志前缀，用于标识查询的表或操作
     * @return 查询结果列表
     */
    public Map<String, Object> executeCommonQueryPrepare(String sql, String logPrefix, Object... params) {
        QueryReq queryReq = new QueryReq();
        queryReq.setPlatId(PLAT_ID);
        queryReq.setSqlStr(sql);
        queryReq.setParams(params);
        LogWriter.info(this, String.format("查询%s,queryReq=%s", logPrefix, JsonUtil.bean2Json(queryReq)));
        try {
            Map<String, Object> result = cfgdbOffsetHandler.commonQueryPrepare(queryReq);
            if (result != null && !result.isEmpty()) {
                LogWriter.info(this, String.format("查询%s成功,属性个数%s。", logPrefix, result.size()));
                return result;
            } else {
                LogWriter.info(this, String.format("查询%s结果为空", logPrefix));
                return Collections.emptyMap();
            }
        } catch (Exception e) {
            LogWriter.error(this, String.format("查询%s异常: %s", logPrefix, e.getMessage()), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 通过商户号+门店号查询终端信息
     */
    public TermDbData getTermByMchntCdAndStoreId(String mchntCd,String storeId) {
        try {
            LogWriter.info(this, String.format("通过富友商户号、门店号调用dboffset 查询终端信息，参数为：mchntCd=%s;storeId=%s", mchntCd, storeId));
            if (StringUtil.isEmpty(mchntCd) && StringUtil.isEmpty(storeId)) {
                LogWriter.info(this, String.format("通过富友商户号+门店号调用dboffset 查询终端信息  参数为空。"));
                return null;
            }
            String sql = " select *  from (SELECT  *,ROW_NUMBER() OVER (ORDER BY rec_upd_ts) AS rn FROM T_TERM_INF " +
                            " WHERE tm_user_cd = ? "+(StringUtils.isEmpty(storeId) ? "" : " AND store_id  = ? ")+" and (tm_model='2DD-FU-DZM' or tm_model='2DD-FU-EP-DZM')  WITH UR) AS term where rn = 1";
            QueryReq queryReq = new QueryReq();
            queryReq.setPlatId(PLAT_ID);
            queryReq.setSqlStr(sql);
            List<Object> paramsList = new ArrayList<>(2);
            paramsList.add(mchntCd);
            if(StringUtils.isNotEmpty(storeId)){
                paramsList.add(storeId);
            }
            Object[] params = new Object[paramsList.size()];
            params=  paramsList.toArray(params);
            queryReq.setParams(params);
            LogWriter.info(this, String.format("查询%s,queryReq=%s", sql, JsonUtil.bean2Json(queryReq)));
            Map<String, Object> result = cfgdbOffsetHandler.commonQueryPrepare(queryReq);
            if (result != null && !result.isEmpty()) {
                LogWriter.info(this, "查询成功："+JsonUtil.bean2Json(result));
                TermDbData termDbData = MapUtils.mapToEntity(result, TermDbData.class);
                return termDbData;
            }
            LogWriter.info(this, String.format("查询结果为空"));
            return null;
        } catch (Exception e) {
            LogWriter.error(this, String.format("通过富友终端号调用dboffset 查询终端信息，发生异常"), e);
            return null;
        }
    }

}
