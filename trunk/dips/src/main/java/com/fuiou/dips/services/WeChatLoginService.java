package com.fuiou.dips.services;

import com.alibaba.fastjson.JSONObject;
import com.fuiou.cacheCenter.mchnt.InsMchntCacheData;
import com.fuiou.dips.consts.Constant;
import com.fuiou.dips.consts.WeChatConsts;
import com.fuiou.dips.data.entity.StoreInfo;
import com.fuiou.dips.data.req.LoginReq;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.data.resp.MchntInfo;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.enums.UserTypeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.persist.beans.DipsUserInf;
import com.fuiou.dips.persist.beans.GroupShopRelate;
import com.fuiou.dips.persist.beans.MchntAcntLoginInfBean;
import com.fuiou.dips.persist.dipsdb.DipsUserInfMapper;
import com.fuiou.dips.persist.jfzwdb.GroupShopRelateMapper;
import com.fuiou.dips.utils.DateUtils;
import com.fuiou.dips.utils.JsonUtil;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.http.FuiouHttpPoster;
import com.mysql.jdbc.log.Log;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class WeChatLoginService {

    @Resource
    private TokenService tokenService;

    @Resource
    private DipsUserInfMapper dipsUserInfMapper;

    @Resource
    private CfgdbService cfgdbService;

    @Resource
    private MchntService mchntService;

    @Resource
    private GroupShopRelateMapper groupShopRelateMapper;

    public LoginResp login(LoginReq loginReq) throws Exception {
        LoginResp loginResp = new LoginResp();
        //获取微信用户信息
        getWeChatUserInfo(loginReq, loginResp);
        //获取用户登录信息
        getUserInfoDbByOpenId(loginResp);
        //写入缓存
        tokenService.setRedisInfo(loginResp);
        return loginResp;
    }

    private void getWeChatUserInfo(LoginReq loginReq, LoginResp loginResp) throws Exception {
        String appid = StringUtils.isBlank(loginReq.getAppId()) ? WeChatConsts.FUIOU_APP_ID : StringUtils.trim(
                loginReq.getAppId());
        String secret = WeChatConsts.APP_SECRET_MAP.get(appid);
        if (StringUtils.isBlank(secret)) {
            throw new FUException(ResponseCodeEnum.APP_NOT_EXIST);
        }
        Map<String, String> params = new HashMap<String, String>();
        params.put("appid", appid);
        params.put("secret", secret);
        params.put("js_code", loginReq.getCode());
        params.put("grant_type", "authorization_code");

        FuiouHttpPoster http = new FuiouHttpPoster();
        http.setUrl(WeChatConsts.WX_UNIONID_PREFIX);  //auth.code2Session3
        LogWriter.info(this, String.format("auth.code2Session3授权请求：%s", JsonUtil.bean2Json(params)));
        String respContent = http.postUTF8(params, 5000);
        LogWriter.info(this, String.format("auth.code2Session3授权响应：%s", respContent));
        JSONObject responseData = JSONObject.parseObject(respContent);
        String unionid = responseData.getString("unionId");
        String openId = responseData.getString("openid");   //小程序用户唯一id
        String sessionKey = responseData.getString("session_key"); //会话密钥
        if (StringUtils.isBlank(openId)) {
            throw new FUException(ResponseCodeEnum.WECHAT_LOGIN_FAIL);
        }
        loginResp.setToken(tokenService.generateToken());
        loginResp.setUnionid(unionid);
        loginResp.setOpenid(openId);
        loginResp.setWechatAuthed(true);
        loginResp.setAppId(appid);
        loginResp.setExpireTime(System.currentTimeMillis() + 2 * 60 * 60 * 1000);
    }

    private void getUserInfoDbByOpenId(LoginResp loginResp) throws Exception {
        try {
            DipsUserInf user = dipsUserInfMapper.selectByOpenIdLastOne(loginResp.getOpenid());
            if(user == null) {
                LogWriter.info(this, "未绑定账号");
                return;
            }
            if(DateUtils.getDaysBetween(user.getLastLoginTime(), new Date()) > Constant.LOGIN_EXPIRE_DAYS){
                LogWriter.info(this, "登录超过30天");
                return;
            }
            loginResp.setUserType(user.getUserType());
            if(StringUtils.isBlank(user.getMchntCd())){
                LogWriter.info(this, String.format("用户%s未关联商户", loginResp.getOpenid()));
                return;
            }
            InsMchntCacheData insMchntCacheData = mchntService.queryMchntInfo(user.getMchntCd());
            if(insMchntCacheData == null){
                LogWriter.info(this, "商户信息不存在");
                return;
            }
            MchntInfo info = new MchntInfo();
            BeanUtils.copyProperties(info, insMchntCacheData);
            loginResp.setMchntInfo(info);
            loginResp.setAccountLogin(true);
            loginResp.setLoginId(user.getLoginId());
            loginResp.setEmployeeRoleType(user.getUserTp());
            //获取门店列表
            loginResp.setRelateStoreList(getRelateStoreList(getMchntAcntLoginInf(user)));
            loginResp.setFullName(user.getFullName());
            loginResp.setMobile(user.getMobile());
        } catch (Exception e) {
            LogWriter.error(this, "获取用户登录信息异常", e);
        }
    }

    private MchntAcntLoginInfBean getMchntAcntLoginInf(DipsUserInf user){
        if(!UserTypeEnum.STAFF.getCode().equals(user.getUserType())){
            return null;
        }
        if(StringUtils.isBlank(user.getAccountId())){
            return null;
        }
        return cfgdbService.getMchntAcntLogInfByLoginId(user.getAccountId());
    }

    private List<StoreInfo> getRelateStoreList(MchntAcntLoginInfBean mchntAcntLoginInfBean){
        List<StoreInfo> storeInfoList = new ArrayList<StoreInfo>();
        if(mchntAcntLoginInfBean == null){
            return storeInfoList;
        }
        try  {
            if(mchntAcntLoginInfBean.getGroupId() == null || mchntAcntLoginInfBean.getGroupId() == 0){
                //非门店组账号
                StoreInfo storeInfo = new StoreInfo(mchntAcntLoginInfBean.getRelateStoreUserId());
                storeInfoList.add(storeInfo);
                return storeInfoList;
            }
            //门店组账号
            List<GroupShopRelate> groupShopRelateList = groupShopRelateMapper.selecStoreIdsByGroupId(mchntAcntLoginInfBean.getGroupId());
            if(groupShopRelateList != null && !groupShopRelateList.isEmpty()){
                for (GroupShopRelate entry :  groupShopRelateList){
                    StoreInfo storeInfo = new StoreInfo(entry.getShopUserId());
                    storeInfoList.add(storeInfo);
                }
            }
            return storeInfoList;
        } catch (Exception e) {
            LogWriter.error(this, "获取账号关联门店列表异常", e);
            return storeInfoList;
        }
    }

}
