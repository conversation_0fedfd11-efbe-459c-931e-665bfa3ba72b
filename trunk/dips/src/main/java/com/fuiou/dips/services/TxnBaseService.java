package com.fuiou.dips.services;

import com.fuiou.dips.enums.OrderStatusEnum;
import com.fuiou.dips.enums.ProjectEnum;
import com.fuiou.dips.persist.beans.MchntTermInf;
import com.fuiou.dips.persist.beans.TxnLog;
import com.fuiou.dips.utils.LogWriter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TxnBaseService {


    @Resource
    private TermInfoService termInfoService;

    public void saveMchntTermInfo(TxnLog txnLogDB) {
        if(!OrderStatusEnum.PAY_SUCCESS.getCode().equals(txnLogDB.getPayState())){
            return;
        }
        // 保存最近一笔商户终端信息
        saveMchntTermInfo(txnLogDB.getMchntCd(), txnLogDB.getFyTermId(), txnLogDB.getStoreId());
    }


    private void saveMchntTermInfo(String mchntCd, String termId, String storeId) {
        try {
            MchntTermInf mchntTermInf = new MchntTermInf();
            mchntTermInf.setMchntCd(mchntCd);
            mchntTermInf.setTermId(termId);
            mchntTermInf.setStoreId(storeId);
            termInfoService.insertOrUpdateMchntTermInf(mchntTermInf);
        } catch (Exception e) {
            LogWriter.error("保存商户最后一笔交易成功使用的终端信息失败", e);
        }
    }
}
