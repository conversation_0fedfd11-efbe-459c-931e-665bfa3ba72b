<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuiou.dips.persist.dipsdb.ProjectMapper">

    <resultMap id="BaseResultMap" type="com.fuiou.dips.persist.beans.Project">
        <id property="rowId" column="row_id" jdbcType="BIGINT"/>
        <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="mchntCd" column="mchnt_cd" jdbcType="CHAR"/>
        <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
        <result property="projectAmt" column="project_amt" jdbcType="DECIMAL"/>
        <result property="projectSt" column="project_st" jdbcType="CHAR"/>
        <result property="currentStageNo" column="current_stage_no" jdbcType="VARCHAR"/>
        <result property="lockFlag" column="lock_flag" jdbcType="CHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="reserved1" column="reserved1" jdbcType="VARCHAR"/>
        <result property="reserved2" column="reserved2" jdbcType="VARCHAR"/>
        <result property="reserved3" column="reserved3" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        row_id
        ,project_no,project_name,
        mchnt_cd,store_id,project_amt,
        project_st,current_stage_no,lock_flag,
        remark,create_time,update_time,
        reserved1,reserved2,reserved3
    </sql>

    <select id="selectByProjectNoAndMchntCd" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_dips_project
        where project_no = #{projectNo,jdbcType=VARCHAR}
        and mchnt_cd = #{mchntCd,jdbcType=CHAR}
    </select>

    <select id="selectProjectsWithStageAndCustomer" resultType="com.fuiou.dips.data.resp.PageProjectResp">
        SELECT
        p.project_no AS projectNo,
        p.project_name AS projectName,
        p.project_st AS projectSt,
        ps.stage_name AS stageName,
        c.customer_name AS customerName,
        p.reserved1 AS storeName,
        p.create_time AS createTime
        FROM t_dips_project p
        LEFT JOIN t_dips_project_stage ps ON p.project_no = ps.project_no and ps.is_delete='0' and  p.current_stage_no =ps.stage_no
        LEFT JOIN t_dips_project_customer pc ON p.project_no = pc.project_no
        LEFT JOIN t_dips_customer c ON pc.mchnt_cd = c.mchnt_cd AND pc.phone = c.phone
        WHERE p.mchnt_cd = #{mchntCd,jdbcType=CHAR} and ps.mchnt_cd = #{mchntCd,jdbcType=CHAR}
        <if test="storeIds != null and !storeIds.isEmpty()">
            AND p.store_id IN
            <foreach item="storeId" collection="storeIds" open="(" separator="," close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="projectSt != null and projectSt != '' ">
            AND p.project_st = #{projectSt,jdbcType=CHAR}
        </if>
        <if test="customerName != null and customerName != '' ">
            AND c.customer_name = #{customerName,jdbcType=CHAR}
        </if>
        <if test="phone != null and phone != '' ">
            AND pc.phone = #{phone,jdbcType=CHAR}
        </if>
        ORDER BY p.create_time DESC
    </select>

    <select id="countProjectWithStatus"  resultType="com.fuiou.dips.data.resp.CountProjectResp">
        SELECT            COUNT(*) AS total,
        SUM(CASE WHEN p.project_st = '1' THEN 1 ELSE 0 END) AS inProgressCount,
        SUM(CASE WHEN p.project_st = '2' THEN 1 ELSE 0 END) AS closedCount,
        SUM(CASE WHEN p.project_st = '9' THEN 1 ELSE 0 END) AS completedCount
        FROM t_dips_project p
        LEFT JOIN t_dips_project_stage ps ON p.project_no = ps.project_no and ps.is_delete='0' and  p.current_stage_no =ps.stage_no
        LEFT JOIN t_dips_project_customer pc ON p.project_no = pc.project_no
        LEFT JOIN t_dips_customer c ON pc.mchnt_cd = c.mchnt_cd AND pc.phone = c.phone
        WHERE p.mchnt_cd = #{mchntCd,jdbcType=CHAR} and ps.mchnt_cd = #{mchntCd,jdbcType=CHAR}
        <if test="storeIds != null and !storeIds.isEmpty()">
            AND p.store_id IN
            <foreach item="storeId" collection="storeIds" open="(" separator="," close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="customerName != null and customerName != '' ">
            AND c.customer_name = #{customerName,jdbcType=CHAR}
        </if>
        <if test="phone != null and phone != '' ">
            AND pc.phone = #{phone,jdbcType=CHAR}
        </if>
    </select>

    <select id="selectProjectList"  resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_dips_project
        <where>
            <if test="mchntCd != null and mchntCd != ''">
                AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
            </if>

            <if test="storeIds != null and storeIds.size() > 0">
                AND store_id IN
                <foreach collection="storeIds" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </if>

            <if test="startDate != null and endDate != null">
                AND create_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
            </if>

            <if test="startDate != null and endDate == null">
                AND create_time >= #{startDate,jdbcType=TIMESTAMP}
            </if>

            <if test="endDate != null and startDate == null">
                <![CDATA[   AND create_time <= #{endDate,jdbcType=TIMESTAMP} ]]>
            </if>
        </where>
    </select>

    <insert id="insert" keyColumn="row_id" keyProperty="rowId" parameterType="com.fuiou.dips.persist.beans.Project"
            useGeneratedKeys="true">
        insert into t_dips_project (project_no, project_name, mchnt_cd, store_id, project_amt, remark)
        values (#{projectNo,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR}, #{mchntCd,jdbcType=CHAR},
                #{storeId,jdbcType=VARCHAR}, #{projectAmt,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.fuiou.dips.persist.beans.Project">
        update t_dips_project
        <set>
            <if test="projectName != null">
                project_name = #{projectName,jdbcType=VARCHAR},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=VARCHAR},
            </if>
            <if test="projectAmt != null">
                project_amt = #{projectAmt,jdbcType=DECIMAL},
            </if>
            <if test="projectSt != null">
                project_st = #{projectSt,jdbcType=CHAR},
            </if>
            <if test="currentStageNo != null">
                current_stage_no = #{currentStageNo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="reserved1 != null">
                reserved1 = #{reserved1,jdbcType=VARCHAR},
            </if>
            update_time = now(),
        </set>
        where row_id = #{rowId,jdbcType=BIGINT}
    </update>

    <update id="updateProjectStatus">
        update t_dips_project
        set project_st  = #{projectSt,jdbcType=CHAR},
            update_time = now()
        where project_no = #{projectNo,jdbcType=VARCHAR}
          and mchnt_cd = #{mchntCd,jdbcType=CHAR}
    </update>


    <select id="selectByMchntCdAndPhone" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        t1.*
        from t_dips_project t1, t_dips_project_customer t2
        where t1.mchnt_cd = t2.mchnt_cd and t1.mchnt_cd = #{mchntCd,jdbcType=CHAR} and t2.phone = #{phone,jdbcType=CHAR}
    </select>

    <update id="updateByProjectNoAndMchntCd">
        update t_dips_project
        set project_amt = #{projectAmt,jdbcType=DECIMAL},
            update_time = now()
        where project_no = #{projectNo,jdbcType=VARCHAR}
          and mchnt_cd = #{mchntCd,jdbcType=CHAR}
    </update>

    <update id="updateLockFlag">
        update t_dips_project
        set lock_flag   = #{lockFlag,jdbcType=CHAR},
            update_time = now()
        where project_no = #{projectNo,jdbcType=VARCHAR}
          and mchnt_cd = #{mchntCd,jdbcType=CHAR}
          and lock_flag = #{state,jdbcType=CHAR}
    </update>

    <delete id="deleteByProjectNoAndMchntCd" >
        delete
        from t_dips_project
        where project_no = #{projectNo,jdbcType=VARCHAR}
          and mchnt_cd = #{mchntCd,jdbcType=CHAR}
          and project_st = '1' limit 1
    </delete>

    <update id="updateProjectForTxn" parameterType="com.fuiou.dips.persist.beans.Project">
        UPDATE t_dips_project
        <set>
            <if test="currentStageNo != null">
                current_stage_no = #{currentStageNo,jdbcType=VARCHAR},
            </if>
            <if test="projectSt != null">
                project_st = #{projectSt,jdbcType=CHAR},
            </if>
            update_time = now()
        </set>
        WHERE project_no = #{projectNo,jdbcType=VARCHAR}
          AND mchnt_cd = #{mchntCd,jdbcType=CHAR}
    </update>
</mapper>
