package com.fuiou.dips.services;

import com.fuiou.cacheCenter.mchnt.InsMchntCacheData;
import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.convert.MchntConvertMapper;
import com.fuiou.dips.data.entity.StoreInfo;
import com.fuiou.dips.data.req.MchntAccountLoginReq;
import com.fuiou.dips.data.req.MchntChooseReq;
import com.fuiou.dips.data.req.MchntMobileLoginReq;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.data.resp.MchntAccountLoginResp;
import com.fuiou.dips.data.resp.MchntInfo;
import com.fuiou.dips.data.resp.ResponseEntity;
import com.fuiou.dips.enums.EmployeeRoleTypeEnum;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.enums.UserTypeEnum;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.persist.beans.DipsUserInf;
import com.fuiou.dips.persist.beans.GroupShopRelate;
import com.fuiou.dips.persist.beans.MchntAcntLoginInfBean;
import com.fuiou.dips.persist.beans.OperatorInfBean;
import com.fuiou.dips.persist.dipsdb.DipsUserInfMapper;
import com.fuiou.dips.persist.jfzwdb.GroupShopRelateMapper;
import com.fuiou.dips.utils.LogWriter;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class MchntAccountService {

    @Resource
    private CfgdbService cfgdbService;

    @Resource
    private MchntService mchntService;

    @Resource
    private TermInfoService termInfoService;

    @Resource
    private DipsUserInfMapper dipsUserInfMapper;

    @Resource
    private SmsService smsService;

    @Resource
    private CaptchaVerifyService captchaVerifyService;

    @Resource
    private TokenService tokenService;

    @Resource
    private MchntConvertMapper mchntConvertMapper;

    @Resource
    private GroupShopRelateMapper groupShopRelateMapper;

    /**
     * 账户密码登录
     *
     * @param req
     * @return
     */
    public MchntAccountLoginResp accountLogin(MchntAccountLoginReq req) {
        //安全验证
        captchaVerifyService.verify(req.getVcode(), req.getUsername(), req.getPassword());
        //初始化返回信息
        MchntAccountLoginResp resp = initAccountLoginResp(req);
        //获取用户信息
        MchntAcntLoginInfBean mchntAcntLoginInfBean = getMchntAcntLoginInf(req.getUsername(), req.getPassword());
        //获取操作员信息
        OperatorInfBean operatorInfBean = getOperatorInf(req.getUsername(), req.getPassword(), mchntAcntLoginInfBean);
        //确定用户类型，获取账户信息
        setAccountResp(resp, mchntAcntLoginInfBean, operatorInfBean);
        //获取商户列表
        setMchntInfoResp(resp, mchntAcntLoginInfBean, operatorInfBean);
        //保存用户登录信息
        saveUserInfo(mchntAcntLoginInfBean, operatorInfBean, resp, req.getUsername());
        //保存登录缓存商户信息
        setLoginRedis(resp, req);
        return resp;
    }


    /**
     * 选择商户号 并 保存用户信息表默认商户号
     *
     * @param req
     */
    public void chooseMchntAndSet(MchntChooseReq req) {
        LoginResp loginInfo = LoginConstat.getLoginToken();
        String userType = loginInfo.getUserType();
        DipsUserInf userInf = dipsUserInfMapper.selectByLoginIdAndType(loginInfo.getLoginId(), userType);
        //校验用户信息
        checkUser(userInf);
        //校验操作员账号信息
        checkOperatorInfAndSetLoginInfoForChoose(req, loginInfo);
        //校验用户账号信息8
        checkMchntAcntLoginInfAndSetLoginInfoForChoose(req, loginInfo);
        //獲取商戶緩存信息
        InsMchntCacheData insMchntCacheData = mchntService.queryMchntInfo(req.getMchntCd());
        //保存登录缓存信息
        setLoginMchntInfoRedis(insMchntCacheData, loginInfo, req);
        //保存用户信息表默认商户号
        saveUserForChooseMchnt(req, userInf, loginInfo);
    }

    private void checkUser(DipsUserInf userInf) {
        if (userInf == null) {
            LogWriter.error(this, "用户不存在");
            throw new FUException(ResponseCodeEnum.USER_NOT_EXIST);
        }
    }

    private void checkOperatorInfAndSetLoginInfoForChoose(MchntChooseReq req, LoginResp loginInfo) {
        if (!req.getUserTp().equals(EmployeeRoleTypeEnum.BOSS.getCode())) {
            return;
        }
        OperatorInfBean operatorInfBean = cfgdbService.getOperatorInfForLoginId(req.getAccountId());
        if (operatorInfBean == null) {
            LogWriter.error(this, "用户不存在operatorInfBean");
            throw new FUException(ResponseCodeEnum.USER_NOT_EXIST);
        }
        if (!req.getMchntCd().equals(operatorInfBean.getMchntCd())) {
            LogWriter.error(this, "用户不存在operatorInfBean");
            throw new FUException(ResponseCodeEnum.MCHNT_INFO_ERROR);
        }
        //登录用户手机号
        loginInfo.setMobile(operatorInfBean.getMobileNo());
        //姓名
        loginInfo.setFullName(operatorInfBean.getUserNameCn());
        //角色
        //        loginInfo.setEmployeeRoleType(EmployeeRoleTypeEnum.BOSS.getCode());
    }

    private void checkMchntAcntLoginInfAndSetLoginInfoForChoose(MchntChooseReq req, LoginResp loginInfo) {
        if (req.getUserTp().equals(EmployeeRoleTypeEnum.BOSS.getCode())) {
            return;
        }
        MchntAcntLoginInfBean mchntAcntLoginInfBean = cfgdbService.getMchntAcntLogInfByLoginId(req.getAccountId());
        if (mchntAcntLoginInfBean == null) {
            LogWriter.error(this, "用户不存在operatorInfBean");
            throw new FUException(ResponseCodeEnum.USER_NOT_EXIST);
        }
        if (!req.getMchntCd().equals(mchntAcntLoginInfBean.getMchntCd())) {
            LogWriter.error(this, "商户不匹配mchntAcntLoginInfBean");
            throw new FUException(ResponseCodeEnum.MCHNT_INFO_ERROR);
        }
        //登录用户手机号
        loginInfo.setMobile(mchntAcntLoginInfBean.getMobile());
        //姓名
        loginInfo.setFullName(mchntAcntLoginInfBean.getName());
        //账号关联门店信息
        loginInfo.setRelateStoreList(getRelateStoreList(mchntAcntLoginInfBean));
        //角色
        //        loginInfo.setEmployeeRoleType(mchntAcntLoginInfBean.getUserTp());
    }


    private void setLoginRedis(MchntAccountLoginResp resp, MchntAccountLoginReq req) {
        LoginResp loginInfo = LoginConstat.getLoginToken();
        try {
            if (resp.getMchntList() != null && resp.getMchntList().size() == 1) {
                //只有一个商户，则设置登录用户默认绑定商户
                loginInfo.setMchntInfo(resp.getMchntList().get(0));
            } else {
                //清空登录用户默认商户，待后续选择
                loginInfo.setMchntInfo(null);
            }

            loginInfo.setAccountLogin(true);
            loginInfo.setUserType(resp.getUserType());
            loginInfo.setLoginId(req.getUsername());
            resp.setEmployeeRoleType(resp.getEmployeeRoleType());
            loginInfo.setRelateStoreList(resp.getRelateStoreList());
            loginInfo.setFullName(resp.getFullName());
            loginInfo.setMobile(resp.getMobile());
            tokenService.setRedisInfo(loginInfo);
        } catch (Exception e) {
            LogWriter.error(this, "保存用户登录缓存异常", e);
            throw new FUException(ResponseCodeEnum.ACCOUNT_HAS_NOT_LOGIN_ERROR);
        }
    }


    private void setLoginMchntInfoRedis(InsMchntCacheData insMchntCacheData, LoginResp loginInfo, MchntChooseReq req) {
        try {
            MchntInfo info = new MchntInfo();
            BeanUtils.copyProperties(info, insMchntCacheData);
            loginInfo.setMchntInfo(info);
            loginInfo.setAccountLogin(true);
            loginInfo.setEmployeeRoleType(req.getUserTp());
            tokenService.setRedisInfo(loginInfo);
        } catch (Exception e) {
            LogWriter.error(this, "保存用户登录缓存异常", e);
            throw new FUException(ResponseCodeEnum.RUNTIME_EXCEPTION);
        }
    }


    private MchntAccountLoginResp initAccountLoginResp(MchntAccountLoginReq req) {
        MchntAccountLoginResp resp = new MchntAccountLoginResp();
        return resp;
    }

    private void setAccountResp(MchntAccountLoginResp resp, MchntAcntLoginInfBean mchntAcntLoginInfBean,
            OperatorInfBean operatorInfBea)
    {
        if (operatorInfBea != null) {
            resp.setUserType(UserTypeEnum.BOSS.getCode());
            resp.setEmployeeRoleType(EmployeeRoleTypeEnum.BOSS.getCode());
            resp.setMobile(operatorInfBea.getMobileNo());
            resp.setFullName(operatorInfBea.getUserNameCn());
            return;
        }
        resp.setUserType(UserTypeEnum.STAFF.getCode());
        resp.setEmployeeRoleType(mchntAcntLoginInfBean.getUserTp());
        //获取账号隶属门店信息列表
        resp.setRelateStoreList(getRelateStoreList(mchntAcntLoginInfBean));
        resp.setMobile(mchntAcntLoginInfBean.getMobile());
        resp.setFullName(mchntAcntLoginInfBean.getName());
    }

    private List<StoreInfo> getRelateStoreList(MchntAcntLoginInfBean mchntAcntLoginInfBean) {
        List<StoreInfo> storeInfoList = new ArrayList<StoreInfo>();
        try {
            if (mchntAcntLoginInfBean.getGroupId() == null || mchntAcntLoginInfBean.getGroupId() == 0) {
                //非门店组账号
                StoreInfo storeInfo = new StoreInfo(mchntAcntLoginInfBean.getRelateStoreUserId());
                storeInfoList.add(storeInfo);
                return storeInfoList;
            }
            //门店组账号
            List<GroupShopRelate> groupShopRelateList = groupShopRelateMapper.selecStoreIdsByGroupId(
                    mchntAcntLoginInfBean.getGroupId());
            if (groupShopRelateList != null && !groupShopRelateList.isEmpty()) {
                for (GroupShopRelate entry : groupShopRelateList) {
                    StoreInfo storeInfo = new StoreInfo(entry.getShopUserId());
                    storeInfoList.add(storeInfo);
                }
            }
            return storeInfoList;
        } catch (Exception e) {
            LogWriter.error(this, "获取账号关联门店列表异常", e);
            return storeInfoList;
        }
    }


    private MchntAcntLoginInfBean getMchntAcntLoginInf(String userName, String password) {
        MchntAcntLoginInfBean mchntAcntLoginInfBean = cfgdbService.getMchntAcntLogInfForLogin(userName, password);
        if (mchntAcntLoginInfBean == null) {
            return null;
        }
        //校验用户信息
        checkMchntAcntLoginInf(mchntAcntLoginInfBean);
        return mchntAcntLoginInfBean;
    }


    private void checkMchntAcntLoginInf(MchntAcntLoginInfBean mchntAcntLoginInfBean) {
        if (mchntAcntLoginInfBean == null) {
            return;
        }
        if (!"1".equals(mchntAcntLoginInfBean.getUserSt())) {
            throw new FUException(ResponseCodeEnum.ACCOUNT_STATUS_ERROR);
        }
        if (StringUtils.isBlank(mchntAcntLoginInfBean.getUserTp())) {
            throw new FUException(ResponseCodeEnum.ACCOUNT_TYPE_ERROR);
        }
        if (StringUtils.isBlank(mchntAcntLoginInfBean.getMchntCd())) {
            throw new FUException(ResponseCodeEnum.MCHNT_INFO_ERROR);
        }

    }

    private OperatorInfBean getOperatorInf(String userName, String password,
            MchntAcntLoginInfBean mchntAcntLoginInfBean)
    {
        if (mchntAcntLoginInfBean != null) {
            //用户表有记录，不查操作人表
            return null;
        }
        OperatorInfBean operatorInfBean = cfgdbService.getOperatorInfForLogin(userName, password);
        if (operatorInfBean == null) {
            throw new FUException(ResponseCodeEnum.ACCOUNT_NOT_EXIST);
        }
        //校验操作员信息
        checkOperatorInf(operatorInfBean);
        return operatorInfBean;
    }

    private void checkOperatorInf(OperatorInfBean operatorInfBean) {
        if (operatorInfBean == null) {
            throw new FUException(ResponseCodeEnum.ACCOUNT_NOT_EXIST);
        }
        if (!"1".equals(operatorInfBean.getUserSt())) {
            throw new FUException(ResponseCodeEnum.ACCOUNT_STATUS_ERROR);
        }
        if (StringUtils.isBlank(operatorInfBean.getMchntCd())) {
            throw new FUException(ResponseCodeEnum.MCHNT_INFO_ERROR);
        }
    }

    private InsMchntCacheData getMchntInfo(String mchntCd) {
        InsMchntCacheData insMchntCacheData = mchntService.queryMchntInfo(mchntCd);
        checkMchntInfo(insMchntCacheData);
        return insMchntCacheData;
    }

    private void checkMchntInfo(InsMchntCacheData insMchntCacheData) {
        if (insMchntCacheData == null) {
            throw new FUException(ResponseCodeEnum.MCHNT_INFO_ERROR);
        }
    }

    private void setMchntInfoResp(MchntAccountLoginResp resp, MchntAcntLoginInfBean mchntAcntLoginInfBean,
            OperatorInfBean operatorInfBea)
    {
        try {
            //获取主商户信息
            String mchntCd = operatorInfBea != null ? operatorInfBea.getMchntCd() : mchntAcntLoginInfBean.getMchntCd();
            LogWriter.info(this, String.format("主商户号,mchntCd=%s", mchntCd));
            List<MchntInfo> mchntInfoList = new ArrayList<>();
            MchntInfo mchntInfo = mchntConvertMapper.mchntCacheToMchntInfo(getMchntInfo(mchntCd));
            mchntInfo.setUserTp(
                    operatorInfBea != null ? EmployeeRoleTypeEnum.BOSS.getCode() : mchntAcntLoginInfBean.getUserTp());
            mchntInfo.setStoreId(operatorInfBea != null ? null : mchntAcntLoginInfBean.getRelateStoreUserId());
            mchntInfo.setLoginId(
                    operatorInfBea != null ? StringUtils.trim(operatorInfBea.getLoginId()) : StringUtils.trim(
                            mchntAcntLoginInfBean.getLoginId()));
            mchntInfoList.add(mchntInfo);
            //获取子商户信息
            List<MchntInfo> subMchntInfoList = mchntService.querySubMchntInfo(mchntCd);
            if (subMchntInfoList != null && subMchntInfoList.size() > 0) {
                for (MchntInfo subMchntInfo : subMchntInfoList) {
                    subMchntInfo.setUserTp(mchntInfo.getUserTp());
                    subMchntInfo.setStoreId(mchntInfo.getStoreId());
                    subMchntInfo.setLoginId(mchntInfo.getLoginId());
                    mchntInfoList.add(subMchntInfo);
                }
            }
            resp.setMchntList(mchntInfoList);
        } catch (Exception e) {
            LogWriter.error(this, "获取商户信息异常,", e);
            throw new FUException(ResponseCodeEnum.MCHNT_INFO_ERROR);
        }
    }

    private void saveUserInfo(MchntAcntLoginInfBean mchntAcntLoginInfBean, OperatorInfBean operatorInfBean,
            MchntAccountLoginResp resp, String userName)
    {
        try {
            DipsUserInf userInf = dipsUserInfMapper.selectByLoginIdAndType(userName, resp.getUserType());
            if (userInf != null) {
                setUserInfoCommonValues(userInf, resp);
                dipsUserInfMapper.update(userInf);
                return;
            }
            userInf = new DipsUserInf();
            userInf.setLoginId(userName);
            userInf.setUserType(resp.getUserType());
            setUserInfoCommonValues(userInf, resp);
            dipsUserInfMapper.insert(userInf);
        } catch (Exception e) {
            LogWriter.error(this, "保存用户信息异常", e);
            throw new FUException(ResponseCodeEnum.LOGIN_FAIL);
        }
    }

    private void setUserInfoCommonValues(DipsUserInf userInf, MchntAccountLoginResp resp) {
        LoginResp loginInfo = LoginConstat.getLoginToken();
        userInf.setOpenId(loginInfo.getOpenid());
        userInf.setUserTp(resp.getEmployeeRoleType());
        userInf.setLastLoginTime(new Date());
        userInf.setUpdateTime(new Date());
        if (resp.getMchntList() != null || resp.getMchntList().size() == 1) {
            //设置用户默认商户
            userInf.setMchntCd(resp.getMchntList().get(0).getMchntCd());
            //设置登录账号
            userInf.setAccountId(resp.getMchntList().get(0).getLoginId());
            userInf.setFullName(resp.getFullName());
        }
    }

    private void saveUserForChooseMchnt(MchntChooseReq req, DipsUserInf userInf, LoginResp loginInfo) {
        //保存用户默认商户号
        userInf.setMchntCd(req.getMchntCd());
        //用户角色
        userInf.setUserTp(req.getUserTp());
        //设置登录账号
        userInf.setAccountId(req.getAccountId());
        //用户姓名
        userInf.setFullName(loginInfo.getFullName());
        //手机号
        userInf.setMobile(loginInfo.getMobile());
        dipsUserInfMapper.update(userInf);
    }


}
