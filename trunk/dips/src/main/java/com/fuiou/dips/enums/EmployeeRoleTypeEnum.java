package com.fuiou.dips.enums;

import com.fuiou.dips.utils.StringUtil;

import java.util.Arrays;
import java.util.Objects;

/**
 * 员工角色类型枚举
 * t_operator_inf   为老板或分公司用户，统一转为 2 老板角色
 * t_mchnt_acnt_login_inf  user_tp 字段 0 普通收银员 1 超级收银员 2 老板（在这里表里基本没有） 4 门店账号 8 装修通项目经理
 */
public enum EmployeeRoleTypeEnum {


    CASHIER("0", "普通收银员"),
    SUPER_ADMIN("1", "超级管理员"),
    BOSS("2", "老板"),
    STORE("4", "门店账号"),
    DECORATION_MANAGER("8", "项目经理");;

    EmployeeRoleTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private String code;
    private String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsgByCode(String code) {
        if (StringUtil.isBlank(code)) {
            return "";
        }
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().map(
                EmployeeRoleTypeEnum::getMsg).orElse("");
    }

    /**
     * 根据 code 获取枚举值
     */
    public static EmployeeRoleTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
