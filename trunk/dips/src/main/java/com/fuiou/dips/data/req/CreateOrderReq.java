package com.fuiou.dips.data.req;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/***
 * @Description: 支付页信息
 * @Author: Joker
 * @Date: 2025/5/14 18:42
 */
public class CreateOrderReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目编号
     */
    @NotBlank(message = "项目编号不能为空")
    @Size(min = 1,
            max = 32,
            message = "项目编号需在1-32字符")
    private String projectNo;



    /**
     * 商户号
     */
    @NotBlank(message = "商户号不能为空")
    @Size(min = 1,
            max = 20,
            message = "商户号需在5-20字符")
    private String mchntCd;

    /**
     * 交易备注
     *
     * @Author: Joker
     * @Date: 2025/5/15 15:26
     */
    @Size(
            max = 30,
            message = "终端号需在1-30字符")
    private String dsc;


    /**
     * 阶段编号
     */
    @NotBlank(message = "阶段编号不能为空")
    @Size(min = 1,
            max = 32,
            message = "阶段编号需在5-32字符")
    private String stageNo;
    /**
     * 阶段已收款金额，单位分
     */
    @Max(value = 1000000000, message = "阶段已收款金额不能大于10000000")
    @Min(value = 0, message = "阶段已收款金额不能小于0")
    private Integer stageActualAmt;

    /**
     * 本次需支付金额
     */
    @NotNull(message = "支付金额不能为空")
    @Max(value = 1000000000, message = "支付金额不能大于10000000")
    @Min(value = 0, message = "支付金额金额不能小于0")
    private Integer amt;

    /**
     * 支付方式
     *
     * @Author: Joker
     * @Date: 2025/5/15 15:53
     */

    @NotBlank(message = "支付方式不能为空")
    private String payType;
    /**
     * 子商户对应的UserId 用户身份id，对应微信的openid与支付宝的userId
     *
     * @Author: Joker
     * @Date: 2025/5/15 16:07
     */
    private String subUserId;//子商户openid

    /**
     * 子商户appid
     *
     * @Author: Joker
     * @Date: 2025/5/15 16:07
     */

    private String subAppId;
    /**
     * userId 用户身份id，对应微信的openid与支付宝的userId
     *
     * @Author: Joker
     * @Date: 2025/5/15 16:10
     */
    @NotBlank(message = "userId不能为空")
    private String userId;

    /**
     * appid
     *
     * @Author: Joker
     * @Date: 2025/5/15 15:17
     */
    @NotBlank(message = "appId不能为空")
    private String appId;


    /**
     * 小程序app id
     *
     * <AUTHOR>
     * @Date 2020/6/22 11:31
     **/
    private String mpAppid;

    /**
     * 小程序open id
     *
     * <AUTHOR>
     * @Date 2020/6/22 11:32
     **/
    private String mpOpenid;

    public String getProjectNo() {

        return projectNo == null ? null : projectNo.trim();
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }


    public String getMchntCd() {

        return mchntCd == null ? null : mchntCd.trim();
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }


    public String getStageNo() {

        return stageNo == null ? null : stageNo.trim();
    }

    public void setStageNo(String stageNo) {
        this.stageNo = stageNo;
    }



    public Integer getStageActualAmt() {

        return stageActualAmt;
    }

    public void setStageActualAmt(Integer stageActualAmt) {
        this.stageActualAmt = stageActualAmt;
    }

    public Integer getAmt() {

        return amt;
    }

    public void setAmt(Integer amt) {
        this.amt = amt;
    }

    public String getAppId() {

        return appId == null ? null : appId.trim();
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getDsc() {

        return dsc == null ? null : dsc.trim();
    }

    public void setDsc(String dsc) {
        this.dsc = dsc;
    }


    public String getPayType() {

        return payType == null ? null : payType.trim();
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getSubUserId() {

        return subUserId == null ? null : subUserId.trim();
    }

    public void setSubUserId(String subUserId) {
        this.subUserId = subUserId;
    }

    public String getSubAppId() {

        return subAppId == null ? null : subAppId.trim();
    }

    public void setSubAppId(String subAppId) {
        this.subAppId = subAppId;
    }

    public String getUserId() {

        return userId == null ? null : userId.trim();
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMpAppid() {

        return mpAppid == null ? null : mpAppid.trim();
    }

    public void setMpAppid(String mpAppid) {
        this.mpAppid = mpAppid;
    }

    public String getMpOpenid() {

        return mpOpenid == null ? null : mpOpenid.trim();
    }

    public void setMpOpenid(String mpOpenid) {
        this.mpOpenid = mpOpenid;
    }
}
