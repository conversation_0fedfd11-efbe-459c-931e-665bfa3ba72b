package com.fuiou.dips.data.resp;

import com.fuiou.dips.data.entity.StoreInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class LoginResp {

	private String appId = "";
	
	private String openid = "";//用户唯一标识

	private String loginId  = "";//登录ID
	
	private String unionid = "";//用户在开放平台的唯一标识符
	
	private String token = "";

	private boolean isWechatAuthed = false;//是否微信授权成功
	
	private boolean isAccountLogin = false;//是否账号已登录成功

	private long expireTime;

	private String userType = "";

	/**
	 * 员工角色类型
	 */
	private String employeeRoleType = "";

	private String relateStoreUserId = "";
	private List<StoreInfo> relateStoreList = new ArrayList<>(); //账号关联门店

	private String userIp; //用户IP

	private MchntInfo mchntInfo;

	private String fullName; //姓名
	private String  mobile ; //手机号



	public String getRelateStoreUserId() {
		return relateStoreUserId;
	}

	public void setRelateStoreUserId(String relateStoreUserId) {
		this.relateStoreUserId = relateStoreUserId;
	}

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
//		if(StringUtils.isNotBlank(mobile)){
//			this.mobile =  mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
//			return;
//		}
		this.mobile = mobile;
	}

	public String getUserIp() {
		return userIp;
	}

	public void setUserIp(String userIp) {
		this.userIp = userIp;
	}

	public String getLoginId() {
		return loginId;
	}

	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}

	public String getEmployeeRoleType() {
		return employeeRoleType;
	}

	public void setEmployeeRoleType(String employeeRoleType) {
		this.employeeRoleType = employeeRoleType;
	}


	public List<StoreInfo> getRelateStoreList() {
		return relateStoreList;
	}

	public void setRelateStoreList(List<StoreInfo> relateStoreList) {
		this.relateStoreList = relateStoreList;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public MchntInfo getMchntInfo() {
		return mchntInfo;
	}

	public void setMchntInfo(MchntInfo mchntInfo) {
		this.mchntInfo = mchntInfo;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	public String getUnionid() {
		return unionid;
	}

	public void setUnionid(String unionid) {
		this.unionid = unionid;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public boolean isAccountLogin() {
		return isAccountLogin;
	}

	public void setAccountLogin(boolean accountLogin) {
		isAccountLogin = accountLogin;
	}

	public boolean isWechatAuthed() {
		return isWechatAuthed;
	}

	public void setWechatAuthed(boolean wechatAuthed) {
		isWechatAuthed = wechatAuthed;
	}

	public long getExpireTime() {
		return expireTime;
	}

	public void setExpireTime(long expireTime) {
		this.expireTime = expireTime;
	}

	public LoginResp() {
	}
}
