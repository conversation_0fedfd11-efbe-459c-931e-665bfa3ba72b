package com.fuiou.dips.enums;

import java.util.Arrays;

public enum UserTypeEnum {
    BOSS("01", "老板"),
    STAFF("02", "员工"),
    CUSTOMER("03", "客户"),

    ;

    UserTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private String code;
    private String msg;



    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据 code 获取枚举值
     */
    public static UserTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
