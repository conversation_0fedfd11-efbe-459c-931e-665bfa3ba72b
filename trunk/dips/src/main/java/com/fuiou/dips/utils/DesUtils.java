package com.fuiou.dips.utils;

import com.fuiou.dips.consts.Constant;

import javax.crypto.Cipher;
import java.security.Key;

/**
 * DES加密和解密工具,可以对字符串进行加密和解密操作  。 
 */
public class DesUtils {
  
  /** 字符串默认键值     */
  private static String strDefaultKey = "national";

  /** 加密工具     */
  //private static Cipher encryptCipher = null;

  /** 解密工具     */
 // private static Cipher decryptCipher = null;

  /**  
   * 将byte数组转换为表示16进制值的字符串， 如:byte[]{8,18}转换为:0813， 和public static byte[]
   * hexStr2ByteArr(String strIn) 互为可逆的转换过程  
   *   
   * @param arrB  
   *            需要转换的byte数组  
   * @return 转换后的字符串  
   * @throws Exception  
   *             本方法不处理任何异常，所有异常全部抛出  
   */
  public static String byteArr2HexStr(byte[] arrB) throws Exception {
    int iLen = arrB.length;
    // 每个byte用两个字符才能表示，所以字符串的长度是数组长度的两倍   
    StringBuffer sb = new StringBuffer(iLen * 2);
    for (int i = 0; i < iLen; i++) {
      int intTmp = arrB[i];
      // 把负数转换为正数   
      while (intTmp < 0) {
        intTmp = intTmp + 256;
      }
      // 小于0F的数需要在前面补0   
      if (intTmp < 16) {
        sb.append("0");
      }
      sb.append(Integer.toString(intTmp, 16));
    }
    return sb.toString();
  }

  /**  
   * 将表示16进制值的字符串转换为byte数组， 和public static String byteArr2HexStr(byte[] arrB)  
   * 互为可逆的转换过程  
   *   
   * @param strIn  
   *            需要转换的字符串  
   * @return 转换后的byte数组  
   * @throws Exception  
   *             本方法不处理任何异常，所有异常全部抛出  
   * <AUTHOR> href="mailto:<EMAIL>">LiGuoQing</a>  
   */
  public static byte[] hexStr2ByteArr(String strIn) throws Exception {
    byte[] arrB = strIn.getBytes();
    int iLen = arrB.length;

    // 两个字符表示一个字节，所以字节数组长度是字符串长度除以2   
    byte[] arrOut = new byte[iLen / 2];
    for (int i = 0; i < iLen; i = i + 2) {
      String strTmp = new String(arrB, i, 2);
      arrOut[i / 2] = (byte) Integer.parseInt(strTmp, 16);
    }
    return arrOut;
  }

  /**  
   * 默认构造方法，使用默认密钥  
   *   
   * @throws Exception  
   */
  public DesUtils() throws Exception {
   // this(strDefaultKey);
  }

  /**  
   * 指定密钥构造方法  
   *   
   * @param strKey  
   *            指定的密钥  
   * @throws Exception  
   */
 /* public DesUtils(String strKey) throws Exception {
    Security.addProvider(new com.sun.crypto.provider.SunJCE());
  }*/

  /**  
   * 加密字节数组  
   *   
   * @param arrB  
   *            需加密的字节数组  
   * @return 加密后的字节数组  
   * @throws Exception  
   */
  public static byte[] encrypt(byte[] arrB, String keyStr) throws Exception {
	  Cipher  encryptCipher = Cipher.getInstance("DES");
	  Key key = getKey(keyStr.getBytes());
	   encryptCipher.init(Cipher.ENCRYPT_MODE, key);
    return encryptCipher.doFinal(arrB);
  }

  /**  
   * 加密字符串  
   *   
   * @param strIn  
   *            需加密的字符串  
   * @return 加密后的字符串  
   * @throws Exception  
   */
  public static String encrypt(String strIn, String keyStr) throws Exception {
    return byteArr2HexStr(encrypt(strIn.getBytes(), keyStr));
  }

  /**  
   * 解密字节数组  
   *   
   * @param arrB  
   *            需解密的字节数组  
   * @return 解密后的字节数组  
   * @throws Exception  
   */
  public static byte[] decrypt(byte[] arrB, String keyStr) throws Exception {
	  Cipher  decryptCipher = Cipher.getInstance("DES");
	  Key key = getKey(keyStr.getBytes());
	   decryptCipher.init(Cipher.DECRYPT_MODE, key);
    return decryptCipher.doFinal(arrB);
  }

  /**  
   * 解密字符串  
   *   
   * @param strIn  
   *            需解密的字符串  
   * @return 解密后的字符串  
   * @throws Exception  
   */
  public static String decrypt(String strIn, String keyStr) throws Exception {
    return new String(decrypt(hexStr2ByteArr(strIn), keyStr));
  }

  /**  
   * 从指定字符串生成密钥，密钥所需的字节数组长度为8位 不足8位时后面补0，超出8位只取前8位  
   *   
   * @param arrBTmp  
   *            构成该字符串的字节数组  
   * @return 生成的密钥  
   * @throws Exception
   */
  private static Key getKey(byte[] arrBTmp) throws Exception {
    // 创建一个空的8位字节数组（默认值为0）   
    byte[] arrB = new byte[8];

    // 将原始字节数组转换为8位   
    for (int i = 0; i < arrBTmp.length && i < arrB.length; i++) {
      arrB[i] = arrBTmp[i];
    }

    // 生成密钥   
    Key key = new javax.crypto.spec.SecretKeySpec(arrB, "DES");

    return key;
  }

  public static void main(String[] args) {
    try {
      String test = "a0bc9473f0c8a8343b8c0f59bbdb855a3dcfdfcd18140ea8e9c56ac79de49cc7f7f758cad9066ddc51996f100ec2f86234bef33f562d0163d8d8aa200bef1ae378b8f51b7ebf49ea2198930e68b880d079b9d94843ac9629c99117887d0657bb5038de10f13b3ae711301a856215424a887c00cb5156a2cb114411ba321bda8fa8a3f65a7837d59318ee201d1681e7746b6f9818cf1e6ce6";
   //   DesUtils des = new DesUtils("fuiouiipayver1");//自定义密钥   
//      System.out.println("加密前的字符:" + test);
    //  String hou = DesUtils.encrypt(test,"fuiouiipayver1");
    //  System.out.println("加密后的字符:" + hou.length());
      String s = DesUtils.decrypt(test, Constant.ORDER_NO_KEY);
      System.out.println("解密后的字符:" + s);
      
    }
    catch (Exception e) {
      LogWriter.error("异常",e);
    }
  }
}