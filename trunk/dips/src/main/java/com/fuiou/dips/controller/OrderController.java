package com.fuiou.dips.controller;

import com.fuiou.dips.data.ResponseEntityFactory;
import com.fuiou.dips.data.req.CashPayReq;
import com.fuiou.dips.data.req.CreateOrderReq;
import com.fuiou.dips.data.req.CreatePayQrcodeReq;
import com.fuiou.dips.data.req.PayPageInfoReq;
import com.fuiou.dips.data.resp.CreateOrderResp;
import com.fuiou.dips.data.resp.CreatePayQrcodeResp;
import com.fuiou.dips.data.resp.PayPageInfoResp;
import com.fuiou.dips.data.resp.ResponseEntity;
import com.fuiou.dips.enums.ResponseCodeEnum;
import com.fuiou.dips.enums.UserTypeEnum;
import com.fuiou.dips.framework.interceptor.RequiresPermissions;
import com.fuiou.dips.framework.log.LogAnnotation;
import com.fuiou.dips.services.CashService;
import com.fuiou.dips.services.OrderService;
import com.fuiou.dips.services.QrcodePayService;
import com.fuiou.dips.utils.JsonUtil;
import com.fuiou.dips.utils.LogWriter;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 交易订单控制器
 *
 * @Description:
 * @Author: Joker
 * @Date: 2025/5/14 15:33
 */

@RestController
@RequestMapping("/order")
public class OrderController {

    @Resource
    private OrderService orderService;
    @Resource
    private QrcodePayService qrcodePayService;
    @Resource
    private CashService cashService;

    /**
     * 查询支付页信息
     *
     * @param payPageInfoReq :
     * @return: com.fuiou.dips.data.resp.ResponseEntity<com.fuiou.dips.data.resp.PageRespBase < com.fuiou.dips.data.resp.TxnLogResp>>
     * @Author: Joker
     * @Date: 2025/5/14 18:41
     */
    @PostMapping("/queryPayPageInfo")
    @ResponseBody
    @LogAnnotation(value = "订单", methodName = "查询支付页信息")
    @RequiresPermissions(userType = {
            UserTypeEnum.CUSTOMER
    })
    public ResponseEntity<PayPageInfoResp> queryPayPageInfo(@Valid @RequestBody PayPageInfoReq payPageInfoReq) throws Exception {
        PayPageInfoResp pageRespBase = orderService.queryPayPageInfo(payPageInfoReq);
        return pageRespBase != null ? ResponseEntityFactory.ok(pageRespBase) : ResponseEntityFactory.fail(
                ResponseCodeEnum.RUNTIME_EXCEPTION);
    }

    /**
     * 主扫小程序下单
     *
     * @param createOrderReq :
     * @return: com.fuiou.dips.data.resp.ResponseEntity<com.fuiou.dips.data.resp.PayPageInfoResp>
     * @Author: Joker
     * @Date: 2025/5/15 11:26
     */

    @PostMapping("/createOrder")
    @ResponseBody
    @LogAnnotation(value = "订单", methodName = "主扫下单")
    @RequiresPermissions(userType = {
            UserTypeEnum.CUSTOMER
    })
    public ResponseEntity<CreateOrderResp> createOrder(@Valid @RequestBody CreateOrderReq createOrderReq) throws Exception {
        CreateOrderResp createOrderResp = orderService.createOrder(createOrderReq);
        return createOrderResp != null ? ResponseEntityFactory.ok(createOrderResp) : ResponseEntityFactory.fail(
                ResponseCodeEnum.RUNTIME_EXCEPTION);
    }

    /**
     * 创建支付二维码
     * @param req :
     * @return:
     * @Author: bl
     * @Date: 2025/5/15 11:26
     */
    @PostMapping("/createPayQrcode")
    @ResponseBody
    @LogAnnotation(value = "订单", methodName = "创建支付二维码")
    @RequiresPermissions(userType = {
            UserTypeEnum.BOSS,
            UserTypeEnum.STAFF
    })
    public ResponseEntity<CreatePayQrcodeResp> createPayQrcode(@Valid @RequestBody CreatePayQrcodeReq req) throws Exception {
        CreatePayQrcodeResp createOrderReq =  qrcodePayService.createPayQrcode(req);
        return createOrderReq != null ? ResponseEntityFactory.ok(createOrderReq) : ResponseEntityFactory.fail(
                ResponseCodeEnum.RUNTIME_EXCEPTION);
    }

    /**
     * 跳转台卡支付页面
     * @param :mchntCd,qrcodeToken
     * @return:
     * @Author: bl
     * @Date: 2025/5/15 11:26
     */
    @GetMapping("/toPay/{mchntCd}/{qrcodeToken}")
    @ResponseBody
    @LogAnnotation(value = "订单", methodName = "去台卡支付")
    public void toPay(HttpServletRequest request, HttpServletResponse  response, @PathVariable("mchntCd") String mchntCd, @PathVariable("qrcodeToken") String qrcodeToken) throws Exception {
        LogWriter.info("去台卡支付入参：mchntCd=" + mchntCd+";qrcodeToken="+qrcodeToken);
        String toDeccaPayUrl =  qrcodePayService.toDeccaPay(mchntCd,qrcodeToken);
        LogWriter.info("去台卡支付出参：toDeccaPayUrl=" + toDeccaPayUrl);
        response.sendRedirect(toDeccaPayUrl);
    }


    /**
     * 支付结果通知接口
     *
     * @param req :
     * @return: com.fuiou.dips.data.resp.ResponseEntity<com.fuiou.dips.data.resp.CreateOrderResp>
     * @Author: Joker
     * @Date: 2025/5/19 11:16
     */

    @PostMapping("/orderResultNotic")
    @ResponseBody
    @LogAnnotation(value = "订单", methodName = "支付结果通知接口")
    public int orderResultNotic(String req) throws Exception {
        boolean result = orderService.orderResultNotic(req);
        return result ? 1 : 0;
    }


    /**
     * 现金支付
     * @param cashPayReq
     * @return
     * @throws Exception
     */
    @PostMapping("/cash")
    @ResponseBody
    @LogAnnotation(value = "订单", methodName = "现金支付")
    @RequiresPermissions(userType = {
            UserTypeEnum.BOSS,
            UserTypeEnum.STAFF
    })
    public ResponseEntity cashPay(@Valid @RequestBody CashPayReq cashPayReq)  throws Exception{
        LogWriter.info(this, String.format("现金支付，cashPayReq=%s", JsonUtil.bean2Json(cashPayReq)));
        cashService.pay(cashPayReq);
        return ResponseEntityFactory.ok();
    }




}
