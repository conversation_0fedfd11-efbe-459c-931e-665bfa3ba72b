package com.fuiou.dips.services;

import com.fuiou.cacheCenter.term.TermCacheData;
import com.fuiou.dips.consts.LoginConstat;
import com.fuiou.dips.data.req.CashPayReq;
import com.fuiou.dips.data.resp.LoginResp;
import com.fuiou.dips.enums.*;
import com.fuiou.dips.framework.exception.FUException;
import com.fuiou.dips.persist.beans.Project;
import com.fuiou.dips.persist.beans.ProjectStage;
import com.fuiou.dips.persist.beans.TxnLog;
import com.fuiou.dips.persist.dipsdb.TxnLogMapper;
import com.fuiou.dips.swt.data.KbpsDataBean;
import com.fuiou.dips.utils.DateFormatUtils;
import com.fuiou.dips.utils.LogWriter;
import com.fuiou.dips.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class CashService {

    @Resource
    private OrderService orderService;

    @Resource
    private SwtService  swtService;

    @Resource
    private TermInfoService termInfoService;

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectStageService projectStageService;

    @Resource
    private TxnLogMapper  txnLogMapper;

    @Resource
    private TpayOrderService tpayOrderService;

    @Resource
    private MchntService mchntService;

    @Resource
    private TxnBaseService txnBaseService;

    /**
     *  现金支付主流程
     * @param cashPayReq
     * @throws Exception
     */
    public void pay(CashPayReq cashPayReq) throws Exception {

        //  项目信息
        Project project = getAndCheckProject(cashPayReq);
        //  项目阶段信息
        ProjectStage projectStage = getAndCheckProjectStage(cashPayReq);
        //  终端信息
        TermCacheData termCacheData = getAndCheckTermInfo(project, cashPayReq);
        //   执行交易
        doTxn(cashPayReq, termCacheData, project, projectStage);
    }

    private void doTxn(CashPayReq cashPayReq,  TermCacheData termCacheData, Project project, ProjectStage projectStage){
        try {
            // 上锁
            lockStage(projectStage);
            // 创建交易记录
            TxnLog txnLog = createTxnLog(cashPayReq, termCacheData, project, projectStage);
            //  发起交易
            cashPayToSwt(txnLog);
            // 更新交易记录
            updateTxnLog(txnLog);
            // 更新项目阶段
            updateProjectStageData(txnLog);
            // 保存最近交易终端信息
            txnBaseService.saveMchntTermInfo(txnLog);
        } catch (Exception e) {
           LogWriter.error("现金支付交易异常", e);
        } finally {
            //  解锁
            unlockStage(projectStage);
        }
    }

    private TxnLog createTxnLog(CashPayReq cashPayReq, TermCacheData termCacheData, Project project, ProjectStage projectStage){
        try {
            LoginResp loginInfo = LoginConstat.getLoginToken();
            TxnLog txnLog = new TxnLog();
            Date now = new Date();
            String orderNo = OrderService.covertOrderNo(termCacheData.getTmFuiouId(), OrderNumPreEnum.ZXT, loginInfo.getUserIp());
            txnLog.setOrderNo(orderNo);
            txnLog.setTradeDt(DateFormatUtils.format(now, "yyyyMMdd"));
            txnLog.setProjectNo(project.getProjectNo());
            txnLog.setMchntCd(project.getMchntCd());
            txnLog.setStageNo(projectStage.getStageNo());
            txnLog.setStoreId(project.getStoreId());
            txnLog.setOrderType(OrderTypeEnum.CASH.getOrderType());
            txnLog.setTradeType(TradeTypeEnum.POSITIVE_TXN.getCode());
            txnLog.setOrderAmt(new BigDecimal(cashPayReq.getAmt()));
            txnLog.setPayState(OrderStatusEnum.INIT_STATUS.getCode());
            txnLog.setFyTermId(termCacheData.getTmFuiouId());
            txnLog.setOpenId(loginInfo.getOpenid());
            txnLog.setCreateTime(now);
            txnLog.setUpdateTime(now);
            txnLog.setPayTime(now);
            int result = txnLogMapper.insert(txnLog);
            if (result != 1){
                throw new FUException(ResponseCodeEnum.RUNTIME_EXCEPTION);
            }
            return txnLog;
        } catch (Exception e) {
            LogWriter.error("创建交易记录异常", e);
            throw new FUException(ResponseCodeEnum.RUNTIME_EXCEPTION);
        }
    }

    private void cashPayToSwt(TxnLog txnLog) {
        try {
            KbpsDataBean kbpsDataBean = swtService.cashPay(txnLog);
            txnLog.setFyTraceNo(kbpsDataBean.getKbpsTraceNo());
            txnLog.setFyFettleDt(kbpsDataBean.getKbpsSettleDt());
            if(ResponseCodeEnum.SUCCESS.getCode().equals(kbpsDataBean.getRspCd())){
                txnLog.setPayState(OrderStatusEnum.PAY_SUCCESS.getCode());
                txnLog.setRespCode(ResponseCodeEnum.SUCCESS.getCode());
                txnLog.setRespMsg(ResponseCodeEnum.SUCCESS.getMsg());
                return;
            }
            txnLog.setPayState(OrderStatusEnum.PAY_FAIL.getCode());
            txnLog.setRespCode(StringUtils.isNotBlank(kbpsDataBean.getRspCd()) ? kbpsDataBean.getRspCd() : ResponseCodeEnum.TRANS_FAILURE.getCode());
            txnLog.setRespMsg(StringUtils.isNotBlank(kbpsDataBean.getRspDataMsg()) ? kbpsDataBean.getRspDataMsg() : ResponseCodeEnum.TRANS_FAILURE.getMsg());
        } catch (Exception e) {
            LogWriter.error("发送swt交易异常", e);
            txnLog.setPayState(OrderStatusEnum.PAY_FAIL.getCode());
            txnLog.setRespCode(ResponseCodeEnum.EXCEPTION.getCode());
            txnLog.setRespMsg(ResponseCodeEnum.EXCEPTION.getMsg());
        }

    }



    private void lockStage(ProjectStage projectStage) {
        try {
            int result = projectStageService.updateLock(projectStage.getMchntCd(), projectStage.getProjectNo(), projectStage.getStageNo());
            if(result == 1){
                projectStage.setLockFlag(LockFlagEnum.LOCK.getState());
            }
        } catch (Exception e) {
            LogWriter.error("阶段记录上锁异常", e);
            throw new FUException(ResponseCodeEnum.PROJECT_STATUS_NOT_ALLOWED);
        }
    }

    private void unlockStage(ProjectStage projectStage) {
        if (!LockFlagEnum.LOCK.getState().equals(projectStage.getLockFlag())){
            LogWriter.info("未锁定成功，无需解锁");
        }
        try {
            projectStageService.updateUnLock(projectStage.getMchntCd(), projectStage.getProjectNo(), projectStage.getStageNo());
        } catch (Exception e) {
            LogWriter.error("阶段记录解锁异常", e);
        }
    }

    private Project getAndCheckProject(CashPayReq cashPayReq) throws Exception {
        Project projectEntry = projectService.queryProject(cashPayReq.getProjectNo(), cashPayReq.getMchntCd());

        return projectEntry;
    }

    private ProjectStage getAndCheckProjectStage(CashPayReq cashPayReq) {
        ProjectStage projectStage = projectStageService.queryProjectStage(cashPayReq.getProjectNo(), cashPayReq.getMchntCd(), cashPayReq.getStageNo());
        BigDecimal reqStageActualAmt = new BigDecimal(StringUtil.isBlank(cashPayReq.getStageActualAmt())?  "0" : cashPayReq.getStageActualAmt());
        if (projectStage.getStageActualAmt()!=null && reqStageActualAmt.compareTo(projectStage.getStageActualAmt()) != 0) {
            LogWriter.info("项目阶段已收款发生变化，可能存在重复交易，刷新后重试");
            throw new FUException(ResponseCodeEnum.PROJECT_STATE_STATUS_NOT_ALLOWED);
        }
        return projectStage;
    }

    private TermCacheData getAndCheckTermInfo(Project project, CashPayReq cashPayReq){
        TermCacheData termCacheData = termInfoService.getTermId(project.getMchntCd(),project.getStoreId());
        if (termCacheData == null) {
            LogWriter.info("支付关联终端信息不存在");
            throw new FUException(ResponseCodeEnum.TERM_NOT_EXIST);
        }
        if (!StringUtil.trimToEmpty(termCacheData.getTmUserCd()).equals(StringUtil.trimToEmpty(cashPayReq.getMchntCd()))) {
            LogWriter.info("支付关联终端所属商户与当前项目所属商户不一致");
            throw new FUException(ResponseCodeEnum.TERM_OWNER_NOT_CURR_MCHNT);
        }
        if (!StringUtil.trimToEmpty(termCacheData.getStoreId()).equals(StringUtil.trimToEmpty(project.getStoreId()))) {
            LogWriter.info("支付关联终端所属门店与当前项目所属门店不一致");
            throw new FUException(ResponseCodeEnum.TERM_OWNER_NOT_CURR_MCHNT);
        }
        return termCacheData;
    }

    private void updateTxnLog(TxnLog txnLog){
        try {
            int result = txnLogMapper.update(txnLog);
            if(result != 1) {
                throw new FUException(ResponseCodeEnum.DB_EXCEPTION);
            }
        } catch (Exception e) {
            LogWriter.error(this,"更新订单信息失败", e);
            txnLog.setPayState(OrderStatusEnum.PAY_FAIL.getCode());
            txnLog.setRespCode(ResponseCodeEnum.EXCEPTION.getCode());
            txnLog.setRespMsg(ResponseCodeEnum.EXCEPTION.getMsg());
            throw new FUException(ResponseCodeEnum.RUNTIME_EXCEPTION);
        }
    }

    private void updateProjectStageData(TxnLog txnLogDB){
        if(!OrderStatusEnum.PAY_SUCCESS.getCode().equals(txnLogDB.getPayState())){
            LogWriter.info("订单状态非成功，无需更新项目阶段");
            return;
        }
        //查询阶段列表
        List<ProjectStage> projectStages = projectStageService.selectByMchntCdAndProjectNo(txnLogDB.getMchntCd(),  txnLogDB.getProjectNo());
        //获取阶段序号
        int stageOrderNo = getTxnStateOrderNo(projectStages, txnLogDB);
        ProjectStage projectStageThisTxn = projectStages.get(stageOrderNo);
        projectStageThisTxn.setOldStageActualAmt(projectStageThisTxn.getStageActualAmt());
        //实收金额
        projectStageThisTxn.setStageActualAmt(projectStageThisTxn.getStageActualAmt().add(txnLogDB.getOrderAmt()));
        BigDecimal refundAmt = projectStageThisTxn.getRefundAmt() == null ? BigDecimal.ZERO : projectStageThisTxn.getRefundAmt();
        LogWriter.info(this, String.format("已退款金额: %s", refundAmt.toPlainString()));
        BigDecimal stageActualAmtConversion = projectStageThisTxn.getStageActualAmt().subtract(refundAmt);
        LogWriter.info(this, String.format("实际已收款金额（计算退款） = 实际收款金额 - 退款金额 = %s", stageActualAmtConversion.toPlainString()));
        //当前阶段收款金额+现金收款金额-退款金额>=当前阶段应收款金额时，自动更改状态为“已完成收款”
        if (stageActualAmtConversion.compareTo(projectStageThisTxn.getStageAmt()) >= 0){
            projectStageThisTxn.setStageSt(ProjectEnum.StageStEnum.COMPLETED.getState());
        }
        int result = projectStageService.updateStageForTxnWithoutUnLock(projectStageThisTxn);
        if(result == 1 && ProjectEnum.StageStEnum.COMPLETED.getState().equals(projectStageThisTxn.getStageSt())) {
            //开启下一阶段项目，修改状态为进行中
            startNextProjectStage(projectStages, stageOrderNo + 1);
            // 更新项目状态及当前阶段号
            updateProject(projectStages, stageOrderNo + 1);
        }
    }

    private int getTxnStateOrderNo(List<ProjectStage> projectStages, TxnLog txnLogDB){
        for(int i = 0; i < projectStages.size(); i++){
            //本次收款交易对应阶段
            if(projectStages.get(i).getStageNo().equals(txnLogDB.getStageNo())){
                return i;
            }
        }
        throw new FUException(ResponseCodeEnum.STAGE_NON_EXIST);
    }

    private void startNextProjectStage(List<ProjectStage> projectStages, int nextStateOrderNo){
        try {
            if(nextStateOrderNo >= projectStages.size()){
                LogWriter.info(this, "无下一阶段项目");
                return;
            }
            ProjectStage nextProjectStage = projectStages.get(nextStateOrderNo);
            projectStageService.startStageForTxn(nextProjectStage.getMchntCd(), nextProjectStage.getProjectNo(), nextProjectStage.getStageNo());
        } catch (Exception e) {
            LogWriter.error(this,  "开启下一项目阶段失败", e);
        }
    }

    private void updateProject(List<ProjectStage> projectStages, int nextStateOrderNo){
        try {
            Project project = new Project();
            project.setProjectNo(projectStages.get(0).getProjectNo());
            project.setMchntCd(projectStages.get(0).getMchntCd());
            if(nextStateOrderNo >= projectStages.size()){
                LogWriter.info(this, "无下一阶段项目，项目完成");
                project.setProjectSt(ProjectEnum.ProjectStEnum.COMPLETED.getState());
            } else {
                LogWriter.info(this, "有下一阶段项目，项目进行中，更新CurrentStageNo");
                project.setCurrentStageNo(projectStages.get(nextStateOrderNo).getStageNo());
            }
            projectService.updateProjectForTxn(project);
        } catch (Exception e) {
            LogWriter.error(this,  "更新项目失败", e);
        }
    }


}
