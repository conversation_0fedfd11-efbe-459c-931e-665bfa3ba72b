package com.fuiou.dips.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fuiou.dips.consts.Constant;
import com.fuiou.dips.enums.OrderTypeEnum;
import com.fuiou.dips.persist.beans.OrderResultNoticData;
import com.fuiou.dips.persist.beans.tpay.CashCouponRes;
import com.fuiou.dips.persist.beans.tpay.TerminalsOrderInfo;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class TpayParseCashCouponUtil {
	
	/**
	 * 优惠卷
	 */
	private static final String PRO_DETAIL_DISCOUNT = "DISCOUNT";
	
	/**
	 * 代金卷
	 */
	private static final String PRO_DETAIL_COUPON = "COUPON";



	private static final List<String> DECCA_CRM_DK_TYPES = new ArrayList<>(Arrays.asList(
			Constant.DECCA_RMK_TYPE_JF,
			Constant.DECCA_RMK_TYPE_CZ,
			Constant.DECCA_RMK_TYPE_COUPON,
			Constant.DECCA_RMK_TYPE_LEVEL,
			Constant.DECCA_RMK_TYPE_MEMP));



	/**
	 * 转换WPOS返回的信息，重新格式化返回JSON
	 * @return
	 */
	public static String parseCashCoupon(String  promotionDetail) {
		if(StringUtils.isBlank(promotionDetail)) {
			return "";
		}
		try {
			List<CashCouponRes> fullCashlist = JSON.parseArray(promotionDetail,CashCouponRes.class);
			List<CashCouponRes> toCashList = new ArrayList<CashCouponRes>();
			for(CashCouponRes fullCash : fullCashlist) {
				CashCouponRes toCash = new CashCouponRes();
				toCash.setP(fullCash.getPromotion_id());
				toCash.setN(fullCash.getName());
				toCash.setA(fullCash.getAmount());
				toCash.setT(fullCash.getType());
				toCashList.add(toCash);
			}
			return JSON.toJSONString(toCashList);
		}catch (Exception e) {
			LogWriter.error("条码支付解析代金卷出现异常:", e);
			return "";
		}
	}
	
	
	/**
	 * 转换WPOS返回的信息，重新格式化返回JSON
	 * @return
	 */
	private static String parseCashCouponToNull() {
		try {
			List<CashCouponRes> toCashList = new ArrayList<CashCouponRes>();
			CashCouponRes toCash = new CashCouponRes();
			toCash.setP("");
			toCash.setN("");
			toCash.setA("");
			toCash.setT("");
			toCashList.add(toCash);
			return JSON.toJSONString(toCashList);
		}catch (Exception e) {
			LogWriter.error("parseCashCouponToNull 条码支付解析代金卷出现异常:", e);
			return "";
		}
	}
	

	
	private static void setCouponFeeByCashCouponWithDk(TerminalsOrderInfo orderInf){
		LogWriter.info("setCouponFeeByCashCouponWithDk 计算总优惠金额开始:原优惠金额:"+orderInf.getCouponFee()+",实付金额:"+orderInf.getOrderAmt());
		//订单总金额
		BigDecimal totalAmtDecimal = new BigDecimal(orderInf.getOrderAmt()).add(nvlZero(orderInf.getCouponFee()));
		//代金券合计金额
		BigDecimal totalCashCouponB = calTotalCashDiscount(orderInf.getReservedPromotionDetail());
		//优惠总金额	
		BigDecimal couponFeeDecimal = nvlZero(orderInf.getCouponFee()).add(totalCashCouponB);

		if(couponFeeDecimal.intValue() > totalAmtDecimal.intValue() ){
			couponFeeDecimal = totalAmtDecimal;
		}
		//实收金额
		BigDecimal orderAmtDecimal = totalAmtDecimal.subtract(couponFeeDecimal);
		//设值
		orderInf.setCouponFee(couponFeeDecimal.toPlainString());
		orderInf.setOrderAmt(orderAmtDecimal.toPlainString());
		orderInf.setReserve10(calTotalCashCoupon(orderInf.getReservedPromotionDetail()).toPlainString());
		LogWriter.info("setCouponFeeByCashCouponWithDk 计算总优惠金额结束:总优惠金额:"+orderInf.getCouponFee()+",总代金卷:"+totalCashCouponB+",实付金额:"+orderInf.getOrderAmt());
	}
	
	private static void setCouponFeeByCashCouponCommon(TerminalsOrderInfo orderInf){
		LogWriter.info("setCouponFeeByCashCouponCommon 计算总优惠金额开始:原优惠金额:"+orderInf.getCouponFee()+",实付金额:"+orderInf.getOrderAmt());
		//订单总金额
		BigDecimal totalAmtDecimal = new BigDecimal(orderInf.getOrderAmt());
		
		
		
		/*if(isEmptyOrZero(orderInf.getCouponFee())) {
			//代金券合计金额
			BigDecimal totalCashCouponB = calTotalCashDiscount(orderInf.getReservedPromotionDetail());
			orderInf.setCouponFee(totalCashCouponB.toPlainString());
		}
		
		//优惠总金额	
		BigDecimal couponFeeDecimal = nvlZero(orderInf.getCouponFee());*/
		
		//优惠总金额	
		BigDecimal couponFeeDecimal = calTotalCashDiscount(orderInf.getReservedPromotionDetail());
	
		if(couponFeeDecimal.intValue() > totalAmtDecimal.intValue() ){
			couponFeeDecimal = totalAmtDecimal;
		}
		//实收金额
		BigDecimal orderAmtDecimal = totalAmtDecimal.subtract(couponFeeDecimal);
		//设值
		orderInf.setOrderAmt(orderAmtDecimal.toPlainString());
		orderInf.setCouponFee(couponFeeDecimal.toPlainString());
		orderInf.setReserve10(calTotalCashCoupon(orderInf.getReservedPromotionDetail()).toPlainString());
		LogWriter.info("setCouponFeeByCashCouponCommon 计算总优惠金额结束:DISCOUNT优惠金额:"+couponFeeDecimal.toPlainString()+",实付金额:"+orderInf.getOrderAmt());
	} 
	
	private static BigDecimal nvlZero(String amt){
		if(StringUtils.isBlank(amt)){
			return new BigDecimal(0);
		}
		return new BigDecimal(amt);
	}
	

	
	/**
	 * 计算优惠卷总额 解析 type = DISCOUNT 的部分计算总值
	 * @param promotionDetail
	 * @return DISCOUNT优惠卷总额
	 */
	private static BigDecimal calTotalCashDiscount(String promotionDetail) {
		BigDecimal rsCash = new BigDecimal("0");
		if(StringUtils.isBlank(promotionDetail)) {
			return new BigDecimal("0");
		}
		try {
			List<CashCouponRes> cashlist = JSON.parseArray(promotionDetail, CashCouponRes.class);
			for(CashCouponRes cash : cashlist) {
				if(PRO_DETAIL_COUPON.equals(cash.getT())) {
					continue;
				}
				BigDecimal bcash = new BigDecimal(cash.getA());
				rsCash = rsCash.add(bcash);
			}
		}catch (Exception e) {
			LogWriter.error("calTotalCashDiscount 计算代金卷总额出现异常:", e);
			return new BigDecimal("0");
		}
		return rsCash;
	}
	
	
	/**
	 * 计算代金卷总额  解析 type = COUPON 的部分计算总值
	 * @param promotionDetail
	 * @return COUPON代金卷总额
	 */
	private static BigDecimal calTotalCashCoupon(String promotionDetail) {
		BigDecimal rsCash = new BigDecimal("0");
		if(StringUtils.isBlank(promotionDetail)) {
			return new BigDecimal("0");
		}
		try {
			List<CashCouponRes> cashlist = JSON.parseArray(promotionDetail,CashCouponRes.class);
			for(CashCouponRes cash : cashlist) {
				if(PRO_DETAIL_DISCOUNT.equals(cash.getT())) {
					continue;
				}
				BigDecimal bcash = new BigDecimal(cash.getA());
				rsCash = rsCash.add(bcash);
			}
		}catch (Exception e) {
			LogWriter.error("calTotalCashCoupon 计算代金卷总额出现异常:", e);
			return new BigDecimal("0");
		}
		return rsCash;
	}
	
	

	/**
	 * WPOS返回优惠金额赋给COUPON_FEE
	 */
	private static void setCouponFee(String reserved_coupon_fee,
									TerminalsOrderInfo orderInfo) {
		if(StringUtils.isBlank(reserved_coupon_fee)){
			return ;
		}
		int couponF = 0;
		try {
			couponF = Integer.parseInt(reserved_coupon_fee.trim());
		} catch (Exception e) {
			LogWriter.error("异常",e);
			couponF = 0;
		}
		if(couponF == 0){
			return ;
		}
		orderInfo.setCouponFee(reserved_coupon_fee);
	}
	
	/**
	 * 检查代金卷JSON是否超长
	 */
	public static void checkPromotionDetailLenth(TerminalsOrderInfo orderInf) {
		if(StringUtils.isBlank(orderInf.getReservedPromotionDetail())) {
			return;
		}
		//超过字段长度，则存空JSON串
		if(orderInf.getReservedPromotionDetail().length() > 300) {
			LogWriter.info(String.format("mchnt_order_no=%s,代金券json长度超过300，数据库字段记录空json串，promotionDetail=%s",orderInf.getMchntOrderNo(),orderInf.getReservedPromotionDetail()));
			orderInf.setReservedPromotionDetail(parseCashCouponToNull());
		}
	}

	/**
	 * @Description: 计算微信代金券
	 * @param queryOrder :
	 * @return:
	 * @Author: chengjun
	 * @Date: 2020/2/11 12:16
	 */
	public static  void calculateWechatFee(TerminalsOrderInfo queryOrder, String totalAmt, String settlementAmt ) {

		if (reserver2IsFee(queryOrder.getReserve2())) {
			//有抵扣 且 有代金卷
			queryOrder.setCouponFee(queryOrder.getCouponFee());
		}
		//口碑和微信直连还是采用protionDetail 来计算优惠金额
		if(withPromotionDetail(queryOrder.getOrderType()))
		{
			queryOrder.setCouponFee(queryOrder.getCouponFee());
			setCouponFeeByCashCouponWithProtionDetail(queryOrder);
		}
		else {
			//重新计算优惠金额(包含代金卷)
			setCouponFeeByCashCouponWithDk(queryOrder,settlementAmt,totalAmt);

		}

	}

	/**
	* @Description:  判断是否抵扣类型
	* @param reserve2 :
	* @return:
	* @Author: chengjun
	* @Date: 2020/2/13 15:22
	*/
	private  static boolean reserver2IsFee(String reserve2){
		return DECCA_CRM_DK_TYPES.contains(reserve2);

	}

	/**
	 * <AUTHOR>
	 * @Description
	 * @Date 2020/6/1 17:33
	 * @param orderInf 订单对象
	 * @param settlement_amt  wpos实收金额，不包含crm部分
	 * @param totalAmount wpos总金额，不包含crm部分
	 * @return void
	 **/
	private static void setCouponFeeByCashCouponWithDk(TerminalsOrderInfo orderInf,String  settlement_amt,String  totalAmount){
		LogWriter.info(String.format("setCouponFeeByCashCoupon 订单编号:%s,计算总优惠金额开始:crm优惠金额:%s,wpos 返回订单总金额:%s,实收金额:",orderInf.getMchntOrderNo(),orderInf.getCouponFee(),totalAmount,settlement_amt));
		if(withPromotionDetail(orderInf.getOrderType()))
		{
			LogWriter.info(String.format("setCouponFeeByCashCouponWithDk 订单类型为ALKB 或者WXZL ,不重新计算优惠金额 订单编号:%s,订单类型",orderInf.getMchntOrderNo(),orderInf.getOrderType()));
			return;
		}
		//CRM优惠金额
		BigDecimal crmFeeAmt =nvlZero(orderInf.getCouponFee());

		//订单金额 如果wpos 返回的reserved_settlement_amt不为空，则取这个，为空则取total_amount
		BigDecimal orderAmt =nvlZero(StringUtil.isEmpty(settlement_amt)?totalAmount:settlement_amt);

		//wpos优惠金额=total_amount-reserved_settlement_amt
		BigDecimal wposFeeAmt =nvlZero(totalAmount).subtract(orderAmt);

		//优惠总金额 =crm优惠+wpos通道侧优惠
		BigDecimal couponFeeDecimal = crmFeeAmt.add(wposFeeAmt);
		//设值
		orderInf.setCouponFee(couponFeeDecimal.toPlainString());
		orderInf.setOrderAmt(orderAmt.toPlainString());
		orderInf.setReserve10(wposFeeAmt.toPlainString());
		LogWriter.info(String.format("setCouponFeeByCashCoupon 计算总优惠金额结束:总优惠金额:%s,实付金额:%s,wpos通道侧优惠金额为:%s",orderInf.getCouponFee(),orderInf.getOrderAmt(),orderInf.getReserve10()));
	}

	/***
	 * <AUTHOR>
	 * @Description   口碑和微信直连交易只根据PromotionDetail计算订单优惠信息
	 * @Date 2020/6/2 10:20
	 * @param orderType
	 * @return boolean
	 **/
	private static boolean withPromotionDetail(String orderType)
	{
		return Arrays.asList(OrderTypeEnum.ALKB.getOrderType(),OrderTypeEnum.WXZL.getOrderType()).contains(orderType);
	}

	/**
	 * 计算优惠金额
	 */

	private static void setCouponFeeByCashCouponWithProtionDetail(TerminalsOrderInfo orderInf) {
		try {

			//没有代金卷,直接返回原优惠金额
			if(StringUtils.isBlank(orderInf.getReservedPromotionDetail())) {
				LogWriter.info(String.format("setCouponFeeByCashCouponWithProtionDetail 计算总优惠金额:没有代金卷,直接返回.原优惠金额:%s",orderInf.getCouponFee()));
				return;
			}
			if("01".equals(orderInf.getReserve2()) || "03".equals(orderInf.getReserve2())){
				//有抵扣的
				setCouponFeeByCashCouponWithDk4ProtionDetail(orderInf);
				return;
			}
			setCouponFeeByCashCouponCommon4ProtionDetail(orderInf);
		}catch (Exception e) {
			LogWriter.error("setCouponFeeByCashCouponWithProtionDetail 计算总优惠金额出现异常:", e);
		}

	}

	private static void setCouponFeeByCashCouponWithDk4ProtionDetail(TerminalsOrderInfo orderInf){
		LogWriter.info(String.format("setCouponFeeByCashCouponWithDk4ProtionDetail 计算总优惠金额开始:原优惠金额:%s,实付金额:%s",orderInf.getCouponFee(),orderInf.getOrderAmt()));
		//订单总金额
		BigDecimal totalAmtDecimal = new BigDecimal(orderInf.getOrderAmt()).add(nvlZero(orderInf.getCouponFee()));
		//代金券合计金额
		BigDecimal totalCashCouponB = calTotalCashDiscount(orderInf.getReservedPromotionDetail());
		//优惠总金额
		BigDecimal couponFeeDecimal = nvlZero(orderInf.getCouponFee()).add(totalCashCouponB);

		if(couponFeeDecimal.intValue() > totalAmtDecimal.intValue() ){
			couponFeeDecimal = totalAmtDecimal;
		}
		//实收金额
		BigDecimal orderAmtDecimal = totalAmtDecimal.subtract(couponFeeDecimal);
		//设值
		orderInf.setCouponFee(couponFeeDecimal.toPlainString());
		orderInf.setOrderAmt(orderAmtDecimal.toPlainString());
		orderInf.setReserve10(calTotalCashCoupon(orderInf.getReservedPromotionDetail()).toPlainString());
		LogWriter.info(String.format("setCouponFeeByCashCouponWithDk4ProtionDetail 计算总优惠金额结束:总优惠金额:%s,总代金卷:%s,实付金额:%s",orderInf.getCouponFee(),totalCashCouponB,orderInf.getOrderAmt()));
	}

	private static void setCouponFeeByCashCouponCommon4ProtionDetail(TerminalsOrderInfo orderInf){
		LogWriter.info(String.format("setCouponFeeByCashCouponCommon4ProtionDetail 计算总优惠金额开始:原优惠金额:%s,实付金额:%s",orderInf.getCouponFee(),orderInf.getOrderAmt()));
		//订单总金额
		BigDecimal totalAmtDecimal = new BigDecimal(orderInf.getOrderAmt());



		/*if(isEmptyOrZero(orderInf.getCouponFee())) {
			//代金券合计金额
			BigDecimal totalCashCouponB = calTotalCashDiscount(orderInf.getReservedPromotionDetail());
			orderInf.setCouponFee(totalCashCouponB.toPlainString());
		}

		//优惠总金额
		BigDecimal couponFeeDecimal = nvlZero(orderInf.getCouponFee());*/

		//优惠总金额
		BigDecimal couponFeeDecimal = calTotalCashDiscount(orderInf.getReservedPromotionDetail());

		if(couponFeeDecimal.intValue() > totalAmtDecimal.intValue() ){
			couponFeeDecimal = totalAmtDecimal;
		}
		//实收金额
		BigDecimal orderAmtDecimal = totalAmtDecimal.subtract(couponFeeDecimal);
		//设值
		orderInf.setOrderAmt(orderAmtDecimal.toPlainString());
		orderInf.setCouponFee(couponFeeDecimal.toPlainString());
		orderInf.setReserve10(calTotalCashCoupon(orderInf.getReservedPromotionDetail()).toPlainString());
		LogWriter.info(String.format("setCouponFeeByCashCouponCommon4ProtionDetail 计算总优惠金额结束:DISCOUNT优惠金额:%s,实付金额:%s",couponFeeDecimal.toPlainString(),orderInf.getOrderAmt()));
	}


	public static  void setRealAmtCouponFee(TerminalsOrderInfo queryOrder, OrderResultNoticData resultData){
		// 非口碑的取reserved_settlement_amt，实收金额
		queryOrder.setReceiptAmount(resultData.getReserved_settlement_amt());// 实收金额
		// 口碑的和支付宝的取reserved_fund_bill_list
		String fundBillList = resultData.getReserved_fund_bill_list();
		if(StringUtils.isNotBlank(fundBillList)){
			try {
				JSONObject resJson = JSONObject.parseObject(fundBillList);
				String receipt_amount = (String) resJson.get("receipt_amount");// 实收金额
				queryOrder.setReceiptAmount(receipt_amount);
				String merchant_fund = (String) resJson.get("merchant_fund");// 优惠金额
//                if(InstalmentConstance.InstalmentOrderType.ALKB.getValue().equals(queryOrder.getOrderType())){
//                    queryOrder.setMerchantFund(merchant_fund);// 口碑的记录这个字段
//                }else{
				queryOrder.setCouponFee(merchant_fund);// 其他的记录这个字段
//                }
			} catch (Exception e) {
				LogWriter.info(queryOrder.getMchntOrderNo()+
						"无法解析reserved_fund_bill_list，无法保存实收金额receipt_amount");
			}
		}
	}
}
