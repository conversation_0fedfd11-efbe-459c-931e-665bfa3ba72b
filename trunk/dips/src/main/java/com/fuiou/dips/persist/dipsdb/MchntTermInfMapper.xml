<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fuiou.dips.persist.dipsdb.MchntTermInfMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.fuiou.dips.persist.beans.MchntTermInf">
        <id property="rowId" column="row_id" jdbcType="BIGINT"/>
        <result property="mchntCd" column="mchnt_cd" jdbcType="CHAR"/>
        <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
        <result property="termId" column="term_id" jdbcType="VARCHAR"/>
        <result property="termModel" column="term_model" jdbcType="VARCHAR"/>
        <result property="termType" column="term_type" jdbcType="VARCHAR"/>
        <result property="termSn" column="term_sn" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="reserved1" column="reserved1" jdbcType="VARCHAR"/>
        <result property="reserved2" column="reserved2" jdbcType="VARCHAR"/>
        <result property="reserved3" column="reserved3" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        row_id,mchnt_cd,store_id,term_id,term_model,term_type,term_sn,order_no,create_time,update_time,reserved1,reserved2,reserved3
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.fuiou.dips.persist.beans.MchntTermInf">
        INSERT INTO t_dips_mchnt_term_inf (
            <trim suffixOverrides=",">
                <if test="mchntCd != null">mchnt_cd,</if>
                <if test="storeId != null">store_id,</if>
                <if test="termId != null">term_id,</if>
                <if test="termModel != null">term_model,</if>
                <if test="termType != null">term_type,</if>
                <if test="termSn != null">term_sn,</if>
                <if test="orderNo != null">order_no,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="reserved1 != null">reserved1,</if>
                <if test="reserved2 != null">reserved2,</if>
                <if test="reserved3 != null">reserved3,</if>
            </trim>
        ) VALUES (
            <trim suffixOverrides=",">
                <if test="mchntCd != null">#{mchntCd},</if>
                <if test="storeId != null">#{storeId},</if>
                <if test="termId != null">#{termId},</if>
                <if test="termModel != null">#{termModel},</if>
                <if test="termType != null">#{termType},</if>
                <if test="termSn != null">#{termSn},</if>
                <if test="orderNo != null">#{orderNo},</if>
                <if test="createTime != null">#{createTime},</if>
                <if test="updateTime != null">#{updateTime},</if>
                <if test="reserved1 != null">#{reserved1},</if>
                <if test="reserved2 != null">#{reserved2},</if>
                <if test="reserved3 != null">#{reserved3},</if>
            </trim>
        )
    </insert>

    <!-- 根据ID查询记录 -->
    <select id="selectByMchntAndStoreId"  resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List"/>
        FROM t_dips_mchnt_term_inf
        WHERE mchnt_cd = #{mchntCd}
        <if test=" '' != storeId and null != storeId">
            and store_id = #{storeId}
        </if>
    </select>

    <!-- 更新记录 -->
    <update id="updateByRowId" parameterType="com.fuiou.dips.persist.beans.MchntTermInf">
        UPDATE t_dips_mchnt_term_inf
        <set>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="termId != null">term_id = #{termId},</if>
            <if test="termModel != null">term_model = #{termModel},</if>
            <if test="termType != null">term_type = #{termType},</if>
            <if test="termSn != null">term_sn = #{termSn},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="reserved1 != null">reserved1 = #{reserved1},</if>
            <if test="reserved2 != null">reserved2 = #{reserved2},</if>
            <if test="reserved3 != null">reserved3 = #{reserved3},</if>
        </set>
        WHERE row_id = #{rowId}
    </update>

    <!-- 删除记录 -->
    <delete id="deleteById" parameterType="Long">
        DELETE FROM t_dips_mchnt_term_inf
        WHERE row_id = #{rowId}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT 
            row_id,
            mchnt_cd,
            store_id,
            term_id,
            term_model,
            term_type,
            term_sn,
            order_no,
            create_time,
            update_time,
            reserved1,
            reserved2,
            reserved3
        FROM t_dips_mchnt_term_inf
    </select>
</mapper>